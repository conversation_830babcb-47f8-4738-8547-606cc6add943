<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة مواعيد التلقيح للأطفال</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f9f9f9;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
        }
        
div#mainPage {
    background: #f8f9fa;
}
        .container {
            width: calc(100% - 72px);
            margin: 0;
            margin-right: 72px;
            background: white;
            min-height: 100vh;
            transition: all 0.3s ease;
            padding: 0;
            box-sizing: border-box;
        }

        .container.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        

        header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            margin: 0;
            width: 100%;
            box-sizing: border-box;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .input-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .input-row {
            display: flex;
            gap: 30px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        .input-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        label {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        input[type="text"] {
            padding: 15px;
            font-size: 1.1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            width: 250px;
            text-align: center;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        .error-message {
            color: #e74c3c;
            font-weight: bold;
            text-align: center;
            margin-top: 10px;
            display: none;
        }

        .results-section {
            padding: 30px;
            display: none;
        }

        .results-section h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8rem;
        }

        .pdf-download-section {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 10px;
            border: 2px solid #2196f3;
        }

        .pdf-download-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 10px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .pdf-download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .pdf-download-btn:active {
            transform: translateY(0);
        }

        .pdf-download-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* أنماط قاعدة البيانات */
        .database-section {
            background: #f8f9fa;
            border-top: 3px solid #17a2b8;
            padding: 30px;
            margin-top: 20px;
        }

        .database-section.mini {
            max-height: 400px;
            overflow: hidden;
        }

        .view-all-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .view-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        /* صفحة سجل الأطفال */
        .children-registry-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .children-registry-page.active {
            display: block;
        }

        .children-registry-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .page-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .search-box:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 10px rgba(23, 162, 184, 0.2);
        }

        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        /* صفحة لوحة القيادة */
        .dashboard-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .dashboard-page.active {
            display: block;
        }

        .dashboard-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 3rem;
            opacity: 0.8;
        }

        .stat-content {
            flex: 1;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .dashboard-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .dashboard-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .inventory-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .inventory-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .inventory-section h3 {
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }

        .progress-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .progress-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .progress-percentage {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .inventory-item {
            background: white;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .inventory-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .inventory-quantity {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
            font-size: 0.9rem;
        }

        .dashboard-actions {
            text-align: center;
            margin-top: 30px;
        }

        .dashboard-actions .btn {
            margin: 0 10px;
            padding: 12px 25px;
            font-size: 1rem;
        }

        .stats-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .stat-card h3 {
            color: #6f42c1;
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }

        .stat-description {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .detailed-stats {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .detailed-stats h2 {
            color: #6f42c1;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .stats-table th,
        .stats-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .stats-table tr:hover {
            background: #f8f9fa;
        }

        .vaccine-name-container {
            line-height: 1.3;
        }

        .vaccine-name-container .arabic-text {
            text-align: right;
            direction: rtl;
        }

        .vaccine-name-container .french-text {
            text-align: left;
            direction: ltr;
        }

        /* صفحة تدبير اللقاحات */
        .vaccine-management-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .vaccine-management-page.active {
            display: block;
        }

        .vaccine-management-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .management-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .monthly-planning-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .months-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .month-window {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .month-window:hover {
            border-color: #28a745;
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .month-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        .month-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }

        .month-name-input {
            padding: 5px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
            width: 120px;
        }

        .stock-management-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .stock-item h4 {
            color: #28a745;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .stock-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .stock-input {
            width: 80px;
            padding: 5px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            text-align: center;
        }

        .stock-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
        }

        .stock-status {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .stock-status.good {
            background: #d4edda;
            color: #155724;
        }

        .stock-status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .stock-status.critical {
            background: #f8d7da;
            color: #721c24;
        }

        .notifications-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .notification-item {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }

        .notification-item.warning {
            background: #fff8e1;
            border-left-color: #ff9800;
        }

        .notification-item.success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }

        .prediction-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .prediction-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .prediction-table th,
        .prediction-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .prediction-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .shortage-highlight {
            background: #ffebee !important;
            color: #c62828;
            font-weight: bold;
        }

        /* صفحة تدبير الأدوية */
        .medicine-management-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .medicine-management-page.active {
            display: block;
        }

        .medicine-management-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .medicine-header {
            background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .medicine-form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .medicine-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .form-field {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .form-field label {
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }

        .form-field input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .medicine-list {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }

        .medicine-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .medicine-item:last-child {
            border-bottom: none;
        }

        .medicine-item:hover {
            background: #f8f9fa;
        }

        .medicine-name {
            font-weight: bold;
            color: #495057;
        }

        .medicine-unit {
            color: #6c757d;
            font-size: 0.9em;
        }

        .delete-medicine-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .delete-medicine-btn:hover {
            background: #c82333;
        }

        /* صفحة تنظيم الأسرة */
        .family-planning-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .family-planning-page.active {
            display: block;
        }

        .family-planning-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .family-planning-header {
            background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .contraceptive-form-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .contraceptive-form {
            display: flex;
            gap: 15px;
            align-items: end;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .contraceptive-list {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
        }

        .contraceptive-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            border-bottom: 1px solid #e9ecef;
        }

        .contraceptive-item:last-child {
            border-bottom: none;
        }

        .contraceptive-item:hover {
            background: #f8f9fa;
        }

        .contraceptive-name {
            font-weight: bold;
            color: #495057;
        }

        .contraceptive-type {
            color: #6c757d;
            font-size: 0.9em;
        }

        .delete-contraceptive-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .delete-contraceptive-btn:hover {
            background: #c82333;
        }

        /* أنماط الشريط الجانبي للصفحات */
        .sidebar-menu {
            padding: 16px 0;
            border-bottom: 1px solid #333;
        }

        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #fff;
            text-decoration: none;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .sidebar-menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu-item.active {
            background: #065fd4;
        }

        .sidebar-menu-icon {
            margin-left: 12px;
            font-size: 1.1rem;
        }

        .database-section h2 {
            color: #17a2b8;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .children-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .child-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .child-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            border-color: #17a2b8;
        }

        .child-card.selected {
            border-color: #17a2b8;
            background: #e7f3ff;
        }

        .child-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 8px;
        }

        .child-birth-date {
            color: #666;
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .child-progress {
            font-size: 0.9rem;
            color: #28a745;
        }

        .delete-child-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            float: left;
            margin-top: 10px;
        }

        .delete-child-btn:hover {
            background: #c82333;
        }

        .vaccination-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .vaccination-item.completed {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .vaccination-item.completed .vaccination-age {
            color: #155724;
        }

        .vaccination-item.completed .vaccination-date {
            background: #d1ecf1;
            color: #0c5460;
        }

        .clear-database-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            float: left;
        }

        .clear-database-btn:hover {
            background: #c82333;
        }

        /* الشريط الجانبي مثل YouTube */
        .sidebar {
            position: fixed;
            top: 0;
            right: -280px;
            width: 280px;
            height: 100vh;
            background: #212121;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
            border-left: 1px solid #333;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: #181818;
            padding: 16px 24px;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            background: #ff0000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .sidebar-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }

        .sidebar-subtitle {
            color: #aaa;
            font-size: 0.8rem;
            margin: 0;
        }

        .sidebar-content {
            padding: 0;
        }

        .auth-form {
            background: #181818;
            border-radius: 0;
            padding: 24px;
            margin-bottom: 0;
            border-bottom: 1px solid #333;
        }

        .form-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: right;
        }

        .form-group-sidebar {
            margin-bottom: 12px;
        }

        .form-group-sidebar label {
            color: #fff;
            font-size: 0.85rem;
            margin-bottom: 6px;
            display: block;
            font-weight: 400;
        }

        .form-group-sidebar input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #333;
            border-radius: 2px;
            background: #0f0f0f;
            color: white;
            font-size: 0.9rem;
            transition: border-color 0.2s ease;
        }

        .form-group-sidebar input::placeholder {
            color: #717171;
        }

        .form-group-sidebar input:focus {
            outline: none;
            border-color: #065fd4;
            background: #0f0f0f;
        }

        .sidebar-btn {
            width: 100%;
            padding: 10px 16px;
            background: #065fd4;
            color: white;
            border: none;
            border-radius: 2px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-bottom: 8px;
        }

        .sidebar-btn:hover {
            background: #0856c7;
        }

        .sidebar-btn.secondary {
            background: #606060;
        }

        .sidebar-btn.secondary:hover {
            background: #4a4a4a;
        }

        .sidebar-btn.danger {
            background: #cc0000;
        }

        .sidebar-btn.danger:hover {
            background: #aa0000;
        }

        .toggle-btn {
            background: transparent;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: auto;
        }

        .toggle-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }



        /* شريط جانبي مصغر */
        .mini-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 72px;
            height: 100vh;
            background: #212121;
            border-left: 1px solid #333;
            z-index: 998;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 8px;
        }

        .mini-sidebar.hidden {
            right: -72px;
        }

        .mini-sidebar-toggle {
            width: 56px;
            height: 40px;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-radius: 4px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mini-sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .mini-sidebar-icon {
            width: 40px;
            height: 40px;
            background: #ff0000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .mini-sidebar-icon:hover {
            background: #cc0000;
        }

        .mini-sidebar-pages {
            margin-top: 20px;
        }

        .mini-page-icon {
            width: 40px;
            height: 40px;
            background: transparent;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
            font-size: 1.1rem;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .mini-page-icon:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .mini-page-icon.active {
            background: #065fd4;
            color: #fff;
        }

        .mini-page-icon.active::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #065fd4;
            border-radius: 2px;
        }

        /* تعديل الشريط الجانبي الكامل */
        .sidebar.open ~ .mini-sidebar {
            right: -72px;
        }

        .user-info {
            background: #181818;
            border-bottom: 1px solid #333;
            padding: 16px 24px;
            margin-bottom: 0;
        }

        .user-info h3 {
            color: #fff;
            margin-bottom: 8px;
            font-size: 1rem;
            font-weight: 500;
        }

        .user-info p {
            color: #aaa;
            margin: 4px 0;
            font-size: 0.85rem;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .auth-toggle {
            text-align: center;
            margin-top: 15px;
        }

        .auth-toggle a {
            color: #3498db;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .auth-toggle a:hover {
            text-decoration: underline;
        }

       


        .vaccination-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .vaccination-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .vaccination-age {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .vaccination-vaccines {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .vaccination-date {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e74c3c;
            background: #fff5f5;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .vaccine-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .vaccine-item:hover {
            background: #e9ecef;
        }

       .vaccine-name {
    font-size: 1rem;
    color: #495057;
    font-weight: 500;
    flex: 1;
    text-align: left !important;
    direction: ltr !important;
    background: #e7eaf1;
    padding: 10px;
    border-radius: 28px;
    margin-left: 5px;
}

        .vaccine-location {
            font-size: 0.9rem;
            font-weight: bold;
            color: #fff;
            background: #2196f3;
            padding: 4px 12px;
            border-radius: 15px;
            margin-left: 30px;
            text-align: right;
            direction: rtl;
        }

        .vaccine-location.mouth {
            background: #4caf50;
        }

        .vaccine-location.left-arm {
            background: #ff9800;
        }

        .vaccine-location.right-arm {
            background: #e91e63;
        }

        .vaccine-location.left-thigh {
            background: #9c27b0;
        }

        .vaccine-location.right-thigh {
            background: #3f51b5;
        }

        .market-input {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 10px;
            display: none;
        }

        .market-input label {
            font-size: 1.1rem;
            font-weight: bold;
            color: #e65100;
            text-align: center;
        }

        select {
            padding: 12px 20px;
            font-size: 1.1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #333;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        select:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
        }

        .market-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .market-month {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .haha{
            color: #c5c5c5;
            font-size: 12px;
            margin-left: 5px;
        }
        .market-month h3 {
            color: #ff9800;
            margin-bottom: 15px;
            font-size: 1.3rem;
            text-align: center;
            border-bottom: 2px solid #ff9800;
            padding-bottom: 10px;
        }

        .market-date {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 5px 0;
            text-align: center;
            font-weight: bold;
            color: #e65100;
        }

        .market-date.vaccination-day {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .market-date.vaccination-day::after {
            content: " 💉";
        }

        .market-dates-section {
            margin-top: 15px;
            padding: 12px;
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            display: none;
        }

        .market-dates-section.show {
            display: block;
        }

        .market-dates-title {
            font-size: 1rem;
            font-weight: bold;
            color: #e65100;
            margin-bottom: 8px;
            text-align: center;
        }

        .market-dates-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .market-date-item {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .market-date-item.exact-match {
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* أنماط خاصة للنصوص العربية والفرنسية */
        .arabic-text {
            text-align: right;
            direction: rtl;
        }

        .french-text {
            text-align: left;
            direction: ltr;
           
        }

        .vaccination-age {
            text-align: left;
            direction: ltr;
        }

        .vaccination-date {
            text-align: center;
            direction: ltr;
        }

        /* معلومات المطور */
        .developer-info {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            margin-top: 30px;
            border-top: 3px solid #3498db;
        }

        .developer-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
        }

        .developer-avatar {
    font-size: 3rem;
    background: linear-gradient(135deg, #46a24a, #7452ac);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}
        .developer-details {
            text-align: left;
        }

        .developer-name {
    font-size: 1.4rem;
    font-weight: bold;
    color: #ffffff;
    text-transform: uppercase;
    margin-bottom: 5px;
}

        .developer-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 3px;
        }

        .developer-subtitle {
            font-size: 0.9rem;
            color: #bdc3c7;
            font-style: italic;
        }

        .developer-credit {
            font-size: 0.9rem;
            color: #95a5a6;
            border-top: 1px solid #34495e;
            padding-top: 15px;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            .input-section, .results-section {
                padding: 20px;
            }
            
            input[type="text"] {
                width: 100%;
                max-width: 300px;
            }
            
            .form-group {
                gap: 10px;
            }

            .input-row {
                flex-direction: column;
                gap: 15px;
            }
            
            .vaccination-item {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            header h1 {
                font-size: 1.5rem;
            }

            header p {
                font-size: 1rem;
            }

            .vaccination-age {
                font-size: 1.1rem;
            }

            .vaccination-vaccines {
                font-size: 1rem;
            }

            .vaccination-date {
                font-size: 1.1rem;
            }

            .vaccine-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .vaccine-name {
                font-size: 0.9rem;
            }

            .vaccine-location {
                font-size: 0.8rem;
                align-self: flex-end;
            }

            .market-input {
                padding: 15px;
                gap: 10px;
            }

            .market-dates-list {
                gap: 5px;
            }

            .market-date-item {
                font-size: 0.8rem;
                padding: 3px 6px;
            }

            select {
                width: 100%;
                max-width: 300px;
            }

            .developer-info {
                padding: 20px 15px;
            }

            .developer-container {
                flex-direction: column;
                gap: 15px;
            }

            .developer-avatar {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .developer-details {
                text-align: center;
            }

            .developer-name {
                font-size: 1.2rem;
            }

            .developer-title {
                font-size: 1rem;
            }

            .developer-subtitle {
                font-size: 0.8rem;
            }

            .developer-credit {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>


    <!-- الطبقة الشفافة -->
    <div class="overlay" id="overlay"></div>

    <!-- الشريط الجانبي المصغر -->
    <div class="mini-sidebar" id="miniSidebar">
        <button class="mini-sidebar-toggle" id="toggleSidebar">☰</button>
        <div class="mini-sidebar-icon" onclick="toggleSidebar()">⚕️</div>

        <!-- أيقونات الصفحات -->
        <div class="mini-sidebar-pages" id="miniSidebarPages" style="display: none;">
            <div class="mini-page-icon active" onclick="showMainPage()" title="الصفحة الرئيسية">
                🏠
            </div>
            <div class="mini-page-icon" onclick="showDashboard()" title="لوحة القيادة">
                📊
            </div>
            <div class="mini-page-icon" onclick="showVaccineManagement()" title="تدبير اللقاحات">
                💉
            </div>
            <div class="mini-page-icon" onclick="showMedicineManagement()" title="تدبير الأدوية">
                💊
            </div>
            <div class="mini-page-icon" onclick="showFamilyPlanningManagement()" title="تنظيم الأسرة">
                👨‍👩‍👧‍👦
            </div>
            <div class="mini-page-icon" onclick="showChildrenRegistry()" title="سجل الأطفال">
                👶
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">⚕️</div>
            <div>
                <div class="sidebar-title">نظام إدارة التلقيح</div>
                <div class="sidebar-subtitle">تسجيل الدخول للممرضين</div>
            </div>
            <button class="toggle-btn" id="sidebarToggleBtn">✕</button>
        </div>

        <div class="sidebar-content">
            <!-- معلومات المستخدم المسجل -->
            <div class="user-info" id="userInfo" style="display: none;">
                <h3>مرحباً بك</h3>
                <p><strong>الممرض/ة:</strong> <span id="currentUserName"></span></p>
                <p><strong>المركز:</strong> <span id="currentCenter"></span></p>
                <p><strong>الإقليم:</strong> <span id="currentRegion"></span></p>
                <button class="sidebar-btn danger" onclick="logout()">تسجيل الخروج</button>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <div class="auth-form" id="loginForm">
                <div class="form-title">تسجيل الدخول</div>

                <div class="form-group-sidebar">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="loginUsername" placeholder="أدخل اسم المستخدم">
                </div>

                <div class="form-group-sidebar">
                    <label>كلمة المرور:</label>
                    <input type="password" id="loginPassword" placeholder="أدخل كلمة المرور">
                </div>

                <button class="sidebar-btn" onclick="login()">تسجيل الدخول</button>

                <div class="auth-toggle">
                    <a href="#" onclick="showRegisterForm()">إنشاء حساب جديد</a>
                </div>
            </div>

            <!-- نموذج التسجيل -->
            <div class="auth-form" id="registerForm" style="display: none;">
                <div class="form-title">إنشاء حساب جديد</div>

                <div class="form-group-sidebar">
                    <label>اسم الممرض/ة:</label>
                    <input type="text" id="registerName" placeholder="الاسم الكامل">
                </div>

                <div class="form-group-sidebar">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="registerUsername" placeholder="اختر اسم مستخدم">
                </div>

                <div class="form-group-sidebar">
                    <label>كلمة المرور:</label>
                    <input type="password" id="registerPassword" placeholder="اختر كلمة مرور قوية">
                </div>

                <div class="form-group-sidebar">
                    <label>اسم المركز الصحي:</label>
                    <input type="text" id="registerCenter" placeholder="مثال: المركز الصحي الحضري">
                </div>

                <div class="form-group-sidebar">
                    <label>الإقليم:</label>
                    <input type="text" id="registerRegion" placeholder="مثال: الدار البيضاء">
                </div>

                <button class="sidebar-btn" onclick="register()">إنشاء الحساب</button>

                <div class="auth-toggle">
                    <a href="#" onclick="showLoginForm()">لديك حساب؟ سجل الدخول</a>
                </div>
            </div>

            <!-- قائمة الصفحات -->
            <div class="sidebar-menu" id="sidebarMenu" style="display: none;">
                <div class="sidebar-menu-item active" onclick="showMainPage()">
                    <span class="sidebar-menu-icon">🏠</span>
                    <span>الصفحة الرئيسية</span>
                </div>
                <div class="sidebar-menu-item" onclick="showDashboard()">
                    <span class="sidebar-menu-icon">📊</span>
                    <span>لوحة القيادة</span>
                </div>
                <div class="sidebar-menu-item" onclick="showVaccineManagement()">
                    <span class="sidebar-menu-icon">💉</span>
                    <span>تدبير اللقاحات</span>
                </div>
                <div class="sidebar-menu-item" onclick="showMedicineManagement()">
                    <span class="sidebar-menu-icon">💊</span>
                    <span>تدبير الأدوية</span>
                </div>
                <div class="sidebar-menu-item" onclick="showFamilyPlanningManagement()">
                    <span class="sidebar-menu-icon">👨‍👩‍👧‍👦</span>
                    <span>تنظيم الأسرة</span>
                </div>
                <div class="sidebar-menu-item" onclick="showChildrenRegistry()">
                    <span class="sidebar-menu-icon">👶</span>
                    <span>سجل الأطفال</span>
                </div>
            </div>
        </div>
        </div>

        <!-- صفحة لوحة القيادة -->
        <div class="dashboard-page" id="dashboardPage">
            <div class="dashboard-header">
                <h1>📊 لوحة القيادة - إحصائيات المركز الصحي</h1>
                <p>نظرة شاملة على أداء وإحصائيات المركز الصحي</p>
            </div>

            <!-- إحصائيات عامة -->
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-icon">👶</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalChildrenStat">0</div>
                        <div class="stat-label">إجمالي الأطفال المسجلين</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💉</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalVaccinesStat">0</div>
                        <div class="stat-label">أنواع اللقاحات المتوفرة</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">💊</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalMedicinesStat">0</div>
                        <div class="stat-label">أنواع الأدوية المتوفرة</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon">👨‍👩‍👧‍👦</div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalContraceptivesStat">0</div>
                        <div class="stat-label">تقنيات تنظيم الأسرة</div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات التلقيحات -->
            <div class="dashboard-section">
                <h2>💉 إحصائيات التلقيحات</h2>
                <div class="vaccination-progress" id="vaccinationProgress">
                    <!-- سيتم ملء إحصائيات التلقيحات هنا -->
                </div>
            </div>

            <!-- إحصائيات المخزون -->
            <div class="dashboard-section">
                <h2>📦 إحصائيات المخزون</h2>
                <div class="inventory-stats">
                    <div class="inventory-section">
                        <h3>💉 مخزون اللقاحات</h3>
                        <div id="vaccineInventoryStats">
                            <!-- سيتم ملء إحصائيات مخزون اللقاحات هنا -->
                        </div>
                    </div>

                    <div class="inventory-section">
                        <h3>💊 مخزون الأدوية</h3>
                        <div id="medicineInventoryStats">
                            <!-- سيتم ملء إحصائيات مخزون الأدوية هنا -->
                        </div>
                    </div>

                    <div class="inventory-section">
                        <h3>👨‍👩‍👧‍👦 مخزون تنظيم الأسرة</h3>
                        <div id="familyPlanningInventoryStats">
                            <!-- سيتم ملء إحصائيات مخزون تنظيم الأسرة هنا -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="dashboard-actions">
                <button onclick="exportDashboardReport()" class="btn btn-primary">
                    📄 تصدير تقرير شامل
                </button>
                <button onclick="refreshDashboard()" class="btn btn-secondary">
                    🔄 تحديث الإحصائيات
                </button>
            </div>
        </div>

        <!-- صفحة تدبير اللقاحات -->
        <div class="vaccine-management-page" id="vaccineManagementPage">
            <div class="management-header">
                <h1>💉 تدبير مخزون اللقاحات</h1>
                <p>إدارة وتتبع مخزون اللقاحات في المركز الصحي</p>
            </div>

            <div class="monthly-planning-section">
                <h2>📅 تخطيط المخزون الشهري</h2>
                <p>أدخل كمية اللقاحات المطلوبة لكل شهر</p>

                <div class="months-container">
                    <!-- الشهر الأول -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الأول</div>
                            <input type="text" class="month-name-input" id="month1Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="month1Grid">
                            <!-- سيتم ملء بيانات الشهر الأول هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثاني -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثاني</div>
                            <input type="text" class="month-name-input" id="month2Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="month2Grid">
                            <!-- سيتم ملء بيانات الشهر الثاني هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثالث -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثالث</div>
                            <input type="text" class="month-name-input" id="month3Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="month3Grid">
                            <!-- سيتم ملء بيانات الشهر الثالث هنا -->
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="saveMonthlyPlanning()" class="btn btn-success" style="margin-left: 10px;">
                        💾 حفظ التخطيط الشهري
                    </button>
                    <button onclick="resetMonthlyPlanning()" class="btn btn-warning">
                        🔄 إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="notifications-section">
                <h2>🔔 إشعارات الاستخدام</h2>
                <div id="notificationsList">
                    <!-- سيتم عرض الإشعارات هنا -->
                </div>
            </div>

            <div class="stock-management-section">
                <h2>📦 المخزون الحالي (مجموع 3 أشهر)</h2>
                <p>المخزون الإجمالي بناءً على التخطيط الشهري</p>
                <div class="stock-grid" id="stockGrid">
                    <!-- سيتم ملء بيانات المخزون الإجمالي هنا -->
                </div>
                <button onclick="downloadStockReport()" class="btn btn-success" style="margin-top: 15px;">
                    📄 تحميل تقرير المخزون PDF
                </button>
            </div>
        </div>

        <!-- صفحة تدبير الأدوية -->
        <div class="medicine-management-page" id="medicineManagementPage">
            <div class="medicine-header">
                <h1>💊 تدبير مخزون الأدوية</h1>
                <p>إدارة وتتبع مخزون الأدوية في المركز الصحي</p>
            </div>

            <div class="medicine-form-section">
                <h2>➕ إضافة دواء جديد</h2>
                <div class="medicine-form">
                    <div class="form-field">
                        <label for="medicineName">اسم الدواء:</label>
                        <input type="text" id="medicineName" placeholder="أدخل اسم الدواء">
                    </div>
                    <div class="form-field">
                        <label for="medicineUnit">الوحدة:</label>
                        <input type="text" id="medicineUnit" placeholder="مثل: قرص، شراب، أمبولة">
                    </div>
                    <div class="form-field">
                        <label>&nbsp;</label>
                        <button onclick="addMedicine()" class="btn btn-primary">إضافة الدواء</button>
                    </div>
                </div>

                <h3>📋 قائمة الأدوية المتوفرة</h3>
                <div class="medicine-list" id="medicineList">
                    <!-- سيتم عرض الأدوية هنا -->
                </div>
            </div>

            <div class="monthly-planning-section">
                <h2>📅 تخطيط المخزون الشهري للأدوية</h2>
                <p>أدخل كمية الأدوية المطلوبة لكل شهر</p>

                <div class="months-container">
                    <!-- الشهر الأول -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الأول</div>
                            <input type="text" class="month-name-input" id="medicineMonth1Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="medicineMonth1Grid">
                            <!-- سيتم ملء بيانات الشهر الأول هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثاني -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثاني</div>
                            <input type="text" class="month-name-input" id="medicineMonth2Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="medicineMonth2Grid">
                            <!-- سيتم ملء بيانات الشهر الثاني هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثالث -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثالث</div>
                            <input type="text" class="month-name-input" id="medicineMonth3Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="medicineMonth3Grid">
                            <!-- سيتم ملء بيانات الشهر الثالث هنا -->
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="saveMedicineMonthlyPlanning()" class="btn btn-success" style="margin-left: 10px;">
                        💾 حفظ التخطيط الشهري
                    </button>
                    <button onclick="resetMedicineMonthlyPlanning()" class="btn btn-warning">
                        🔄 إعادة تعيين
                    </button>
                </div>
            </div>

            <div class="stock-management-section">
                <h2>📦 المخزون الحالي للأدوية (مجموع 3 أشهر)</h2>
                <p>المخزون الإجمالي بناءً على التخطيط الشهري</p>
                <div class="stock-grid" id="medicineStockGrid">
                    <!-- سيتم ملء بيانات المخزون الإجمالي هنا -->
                </div>
                <button onclick="downloadMedicineStockReport()" class="btn btn-success" style="margin-top: 15px;">
                    📄 تحميل تقرير مخزون الأدوية PDF
                </button>
            </div>
        </div>

        <!-- صفحة تنظيم الأسرة -->
        <div class="family-planning-page" id="familyPlanningPage">
            <div class="family-planning-header">
                <h1>👨‍👩‍👧‍👦 تنظيم الأسرة</h1>
                <p>إدارة وتتبع مخزون وسائل منع الحمل والتنظيم الأسري</p>
            </div>

            <!-- قسم إضافة تقنيات منع الحمل -->
            <div class="contraceptive-form-section">
                <h2>➕ إضافة تقنية منع حمل جديدة</h2>
                <div class="contraceptive-form">
                    <div class="form-field">
                        <label for="contraceptiveName">اسم التقنية:</label>
                        <input type="text" id="contraceptiveName" placeholder="مثل: حبوب منع الحمل، اللولب النحاسي">
                    </div>
                    <div class="form-field">
                        <label>&nbsp;</label>
                        <button onclick="addContraceptive()" class="btn btn-primary">إضافة التقنية</button>
                    </div>
                </div>

                <h3>📋 قائمة تقنيات منع الحمل المتوفرة</h3>
                <div style="margin-bottom: 10px;">
                    <button onclick="addDefaultContraceptives()" class="btn btn-info" style="font-size: 0.9rem;">
                        ➕ إضافة تقنيات افتراضية للاختبار
                    </button>
                </div>
                <div class="contraceptive-list" id="contraceptiveList">
                    <!-- سيتم عرض تقنيات منع الحمل هنا -->
                </div>
            </div>

            <!-- قسم التخطيط الشهري -->
            <div class="monthly-planning-section">
                <h2>📅 تخطيط العدد الشهري لتقنيات منع الحمل</h2>
                <p>أدخل عدد كل تقنية منع حمل المطلوبة لكل شهر</p>

                <div class="months-container">
                    <!-- الشهر الأول -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الأول</div>
                            <input type="text" class="month-name-input" id="familyPlanningMonth1Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="familyPlanningMonth1Grid">
                            <!-- سيتم ملء بيانات الشهر الأول هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثاني -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثاني</div>
                            <input type="text" class="month-name-input" id="familyPlanningMonth2Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="familyPlanningMonth2Grid">
                            <!-- سيتم ملء بيانات الشهر الثاني هنا -->
                        </div>
                    </div>

                    <!-- الشهر الثالث -->
                    <div class="month-window">
                        <div class="month-header">
                            <div class="month-title">الشهر الثالث</div>
                            <input type="text" class="month-name-input" id="familyPlanningMonth3Name"
                                   placeholder="اسم الشهر" value="">
                        </div>
                        <div class="stock-grid" id="familyPlanningMonth3Grid">
                            <!-- سيتم ملء بيانات الشهر الثالث هنا -->
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <button onclick="saveFamilyPlanningMonthlyPlanning()" class="btn btn-success" style="margin-left: 10px;">
                        💾 حفظ التخطيط الشهري
                    </button>
                    <button onclick="refreshFamilyPlanningGrids()" class="btn btn-info" style="margin-left: 10px;">
                        🔄 تحديث الشبكات
                    </button>
                    <button onclick="resetFamilyPlanningMonthlyPlanning()" class="btn btn-warning">
                        🗑️ إعادة تعيين
                    </button>
                </div>
            </div>

            <!-- قسم المجموع الإجمالي -->
            <div class="stock-management-section">
                <h2>📦 المجموع الإجمالي لتقنيات منع الحمل (مجموع 3 أشهر)</h2>
                <p>العدد الإجمالي بناءً على التخطيط الشهري</p>
                <div class="stock-grid" id="familyPlanningStockGrid">
                    <!-- سيتم ملء بيانات المجموع الإجمالي هنا -->
                </div>
                <button onclick="downloadFamilyPlanningStockReport()" class="btn btn-success" style="margin-top: 15px;">
                    📄 تحميل تقرير تنظيم الأسرة PDF
                </button>
            </div>
        </div>

        <!-- صفحة سجل الأطفال -->
        <div class="children-registry-page" id="childrenRegistryPage">
            <button class="back-btn" onclick="showMainPage()">← العودة للصفحة الرئيسية</button>

            <div class="page-header">
                <h1>📋 سجل الأطفال الكامل</h1>
                <p>جميع الأطفال المسجلين في النظام</p>
            </div>

            <input type="text" class="search-box" id="searchBox" placeholder="🔍 البحث عن طفل (الاسم أو تاريخ الولادة)...">

            <div class="children-grid" id="childrenGrid">
                <!-- سيتم ملء هذا القسم بـ JavaScript -->
            </div>
        </div>
    </div>

    <div class="container">
        <!-- الصفحة الرئيسية -->
        <div class="main-page" id="mainPage">
            <header>
                <h1>حاسبة مواعيد التلقيح للأطفال</h1>
                <p>حسب الجدول الوطني المغربي للتلقيح والتغذية</p>
            </header>

        <div class="input-section">
            <div class="form-group">
                <div class="input-row">
                    <div class="input-field">
                        <label for="childName">اسم الطفل:</label>
                        <input type="text" id="childName" placeholder="أدخل اسم الطفل">
                    </div>
                    <div class="input-field">
                        <label for="birthDate">تاريخ الولادة:</label>
                        <input type="text" id="birthDate" placeholder="DD/MM/YYYY" maxlength="10">
                    </div>
                </div>
                <button id="calculateBtn">احسب مواعيد التلقيح</button>
            </div>
            <div class="error-message" id="errorMessage"></div>
        </div>

        <div class="results-section" id="resultsSection">
            <h2 id="resultsTitle">جدول مواعيد التلقيح</h2>
            <div id="childInfo" style="text-align: center; margin-bottom: 20px; font-size: 1.1rem; color: #333; display: none;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                    <strong>اسم الطفل:</strong> <span id="displayChildName"></span> |
                    <strong>تاريخ الولادة:</strong> <span id="displayBirthDate"></span>
                </div>
            </div>

            <div class="market-input" id="marketInput">
                <label for="marketDay">🛒 اختر يوم السوق الأسبوعي لمعرفة التواريخ المناسبة:</label>
                <select id="marketDay">
                    <option value="">-- اختر يوم --</option>
                    <option value="0">الأحد</option>
                    <option value="1">الاثنين</option>
                    <option value="2">الثلاثاء</option>
                    <option value="3">الأربعاء</option>
                    <option value="4">الخميس</option>
                    <option value="5">الجمعة</option>
                    <option value="6">السبت</option>
                </select>
            </div>

            <div class="vaccination-schedule" id="vaccinationSchedule">
            </div>

            <div class="pdf-download-section" id="pdfDownloadSection" style="display: none;">
                <h3 style="color: #1976d2; margin-bottom: 15px;">📄 تحميل النتائج</h3>
                <button class="pdf-download-btn" id="downloadPdfBtn">
                    📥 تحميل جدول التلقيح PDF
                </button>
                <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                    سيتم تحميل ملف PDF يحتوي على صورة كاملة لجدول مواعيد التلقيح
                </p>
            </div>
        </div>

      

        <!-- قسم قاعدة البيانات -->
        <div class="database-section mini">
            <h2>📋 سجل الأطفال</h2>
            <p style="text-align: center; color: #666; margin-bottom: 20px;">
                آخر الأطفال المسجلين (آخر 4 أطفال)
            </p>

            <div class="children-list" id="childrenList">
                <div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">
                    لا توجد سجلات بعد. ابدأ بحساب مواعيد التلقيح لطفل جديد.
                </div>
            </div>

            <div style="text-align: center; margin-top: 0px;">
                <button class="view-all-btn" onclick="showChildrenRegistry()">📋 عرض جميع الأطفال</button>
                <button class="clear-database-btn" id="clearDatabaseBtn" style="display: none;">
                    🗑️ مسح جميع السجلات
                </button>
            </div>
            <div style="clear: both;"></div>
        </div>

        <!-- قسم المطور -->
        <footer class="developer-info">
            <div class="developer-container">
                <div class="developer-avatar">👨‍⚕️</div>
                <div class="developer-details">
                    <div class="haha"> تمت برمجة هذه الأداة من طرف </div>
                    <div class="developer-name professional-text">Jamal Chafik</div>
                    <div class="developer-title">Infirmier Diplômé d'État</div>
                    <div class="developer-subtitle">Infirmier Polyvalent</div>
                </div>
            </div>
            <div class="developer-credit">
                version 1.0
            </div>
        </footer>
    </div>

    <!-- مكتبة jsPDF لإنشاء ملفات PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        const vaccinationSchedule = [
            {
                age: "naissance",
                months: 0,
                additionalDays: 0,
                vaccines: [
                    { name: "HB1 (0.5 ml en IM)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "1 mois",
                months: 1,
                additionalDays: 0,
                vaccines: [
                    { name: "BCG (0.05ml en ID)", location: "يد يسرى" },
                    { name: "VPO0 (2 gouttes)", location: "الفم" },
                    { name: "Vit D 100 000 ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "2 mois",
                months: 2,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta1 (DTC+Hib+HB) (0.5 ml en IM)", location: "فخد يمنى" },
                    { name: "VPO1", location: "الفم" },
                    { name: "Rota1 (2 ml)", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "2.5 mois",
                months: 2,
                additionalDays: 15,
                vaccines: [
                    { name: "VPC 1 (Pneumo 1) (0.5 ml en IM)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "3 mois",
                months: 3,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta2 (DTC+Hib+HB)", location: "فخد يمنى" },
                    { name: "VPO2", location: "الفم" },
                    { name: "Rota2", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "4 mois",
                months: 4,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta3 (DTC+Hib+HB)", location: "فخد يمنى" },
                    { name: "VPO3", location: "الفم" },
                    { name: "Rota3", location: "الفم" },
                    { name: "VPI1 (0.5 ml)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "4.5 mois",
                months: 4,
                additionalDays: 15,
                vaccines: [
                    { name: "VPC 2 (Pneumo 2)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "6 mois",
                months: 6,
                additionalDays: 0,
                vaccines: [
                    { name: "VPC 3 (Pneumo 3)", location: "فخد يسرى" },
                    { name: "Vit D 100 000ui", location: "الفم" },
                    { name: "Vit A 100 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "9 mois",
                months: 9,
                additionalDays: 0,
                vaccines: [
                    { name: "RR1 (rougeole-rubéole combiné) (0.5 ml en s/c)", location: "يد يسرى" },
                    { name: "VPI2 (0.5ml)", location: "فخد يسرى" },
                    { name: "Vit A 200 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "12 mois",
                months: 12,
                additionalDays: 0,
                vaccines: [
                    { name: "VPC 4 (Pneumo 4)", location: "يد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "18 mois",
                months: 18,
                additionalDays: 0,
                vaccines: [
                    { name: "VPO4", location: "الفم" },
                    { name: "DTC rappel 1 (0.5 ml en IM)", location: "يد يسرى" },
                    { name: "RR2", location: "يد يمنى" },
                    { name: "Vit A 200 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "5 ans",
                months: 60,
                additionalDays: 0,
                vaccines: [
                    { name: "VPO5", location: "الفم" },
                    { name: "DTC rappel 2", location: "يد يسرى" }
                ],
                additionalInfo: ""
            }
        ];

        const childNameInput = document.getElementById('childName');
        const birthDateInput = document.getElementById('birthDate');
        const calculateBtn = document.getElementById('calculateBtn');
        const errorMessage = document.getElementById('errorMessage');
        const resultsSection = document.getElementById('resultsSection');
        const vaccinationScheduleDiv = document.getElementById('vaccinationSchedule');
        const marketInput = document.getElementById('marketInput');
        const marketDay = document.getElementById('marketDay');
        const pdfDownloadSection = document.getElementById('pdfDownloadSection');
        const downloadPdfBtn = document.getElementById('downloadPdfBtn');
        const childrenList = document.getElementById('childrenList');
        const clearDatabaseBtn = document.getElementById('clearDatabaseBtn');

        let currentVaccinationDates = [];
        let currentBirthDate = null;
        let currentChildId = null;
        let childrenDatabase = [];
        let currentUser = null;
        let usersDatabase = [];

        calculateBtn.addEventListener('click', calculateVaccinationDates);
        marketDay.addEventListener('change', updateMarketDates);
        downloadPdfBtn.addEventListener('click', generatePDF);
        clearDatabaseBtn.addEventListener('click', clearDatabase);

        // إعداد الشريط الجانبي
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const toggleBtn = document.getElementById('toggleSidebar');
        const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
        const miniSidebar = document.getElementById('miniSidebar');

        toggleBtn.addEventListener('click', toggleSidebar);
        sidebarToggleBtn.addEventListener('click', closeSidebar);
        overlay.addEventListener('click', closeSidebar);

        // تحميل قاعدة البيانات عند بدء التشغيل
        loadDatabase();
        loadUsers();
        checkCurrentUser();
        initializeSidebar();
        setupSearch();

        birthDateInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateVaccinationDates();
            }
        });

        birthDateInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2);
            }
            if (value.length >= 5) {
                value = value.substring(0, 5) + '/' + value.substring(5, 9);
            }
            e.target.value = value;
        });

        function isValidDate(dateString) {
            const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
            const match = dateString.match(regex);

            if (!match) return false;

            const day = parseInt(match[1], 10);
            const month = parseInt(match[2], 10);
            const year = parseInt(match[3], 10);

            if (month < 1 || month > 12) return false;
            if (day < 1 || day > 31) return false;
            if (year < 1900 || year > new Date().getFullYear()) return false;

            const date = new Date(year, month - 1, day);
            return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year;
        }

        function parseDate(dateString) {
            const parts = dateString.split('/');
            return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }

        function formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        function addMonthsAndDays(date, months, additionalDays) {
            function getDaysInMonth(year, month) {
                return new Date(year, month + 1, 0).getDate();
            }

            let result = new Date(date);
            const originalDay = date.getDate();

            // حساب الأشهر بدقة - عد الأيام الفعلي من اليوم المحدد
            for (let i = 0; i < months; i++) {
                const currentYear = result.getFullYear();
                const currentMonth = result.getMonth();
                const currentDay = result.getDate();
                const daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

                // حساب الأيام المتبقية في الشهر الحالي (من اليوم الحالي إلى نهاية الشهر)
                const daysRemainingInMonth = daysInCurrentMonth - currentDay + 1;

                // إضافة الأيام المتبقية للانتقال لبداية الشهر التالي
                result.setDate(result.getDate() + daysRemainingInMonth);

                // الآن نحن في اليوم الأول من الشهر التالي
                // نحتاج للوصول لنفس اليوم الأصلي في هذا الشهر الجديد
                const newMonth = result.getMonth();
                const newYear = result.getFullYear();
                const daysInNewMonth = getDaysInMonth(newYear, newMonth);

                // تحديد اليوم المستهدف في الشهر الجديد
                let targetDay;
                if (originalDay <= daysInNewMonth) {
                    targetDay = originalDay;
                } else {
                    // إذا كان اليوم الأصلي غير موجود في الشهر الجديد (مثل 31 في فبراير)
                    targetDay = daysInNewMonth;
                }

                // الانتقال لليوم المستهدف (نطرح 1 لأننا في اليوم الأول)
                result.setDate(targetDay);
            }

            // إضافة الأيام الإضافية
            if (additionalDays > 0) {
                result.setDate(result.getDate() + additionalDays);
            }

            return result;
        }

        function calculateVaccinationDates() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً للوصول إلى هذه الميزة');
                toggleSidebar();
                return;
            }

            const childNameValue = childNameInput.value.trim();
            const birthDateValue = birthDateInput.value.trim();

            errorMessage.style.display = 'none';

            if (!childNameValue) {
                showError('يرجى إدخال اسم الطفل');
                return;
            }

            if (!birthDateValue) {
                showError('يرجى إدخال تاريخ الولادة');
                return;
            }

            if (!isValidDate(birthDateValue)) {
                showError('يرجى إدخال تاريخ صحيح بصيغة DD/MM/YYYY');
                return;
            }

            const birthDate = parseDate(birthDateValue);

            if (birthDate > new Date()) {
                showError('تاريخ الولادة لا يمكن أن يكون في المستقبل');
                return;
            }

            const vaccinationDates = vaccinationSchedule.map(vaccination => {
                const vaccinationDate = addMonthsAndDays(birthDate, vaccination.months, vaccination.additionalDays);
                return {
                    ...vaccination,
                    date: vaccinationDate,
                    formattedDate: formatDate(vaccinationDate)
                };
            });

            currentVaccinationDates = vaccinationDates;
            currentBirthDate = birthDate;

            // حفظ الطفل في قاعدة البيانات
            currentChildId = saveChildToDatabase(childNameValue, birthDateValue, vaccinationDates);

            displayResults(vaccinationDates, childNameValue, birthDateValue);
            marketInput.style.display = 'flex';
            pdfDownloadSection.style.display = 'block';

            // تحديث عرض قاعدة البيانات
            displayChildrenList();
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            resultsSection.style.display = 'none';
        }

        function getLocationClass(location) {
            switch(location) {
                case 'الفم': return 'mouth';
                case 'يد يسرى': return 'left-arm';
                case 'يد يمنى': return 'right-arm';
                case 'فخد يسرى': return 'left-thigh';
                case 'فخد يمنى': return 'right-thigh';
                default: return '';
            }
        }

        function displayResults(vaccinationDates, childName, birthDate, completedVaccinations = {}) {
            // عرض معلومات الطفل
            document.getElementById('displayChildName').textContent = childName;
            document.getElementById('displayBirthDate').textContent = birthDate;
            document.getElementById('childInfo').style.display = 'block';

            vaccinationScheduleDiv.innerHTML = '';

            vaccinationDates.forEach((vaccination, index) => {
                const isCompleted = completedVaccinations && completedVaccinations[index];
                const vaccinationItem = document.createElement('div');
                vaccinationItem.className = `vaccination-item ${isCompleted ? 'completed' : ''}`;

                let vaccinesHtml = '';
                vaccination.vaccines.forEach(vaccine => {
                    const locationClass = getLocationClass(vaccine.location);
                    vaccinesHtml += `
                        <div class="vaccine-item">
                            <div class="vaccine-name french-text">${vaccine.name}</div>
                            <div class="vaccine-location ${locationClass} arabic-text">📍 ${vaccine.location}</div>
                        </div>
                    `;
                });

                const checkboxHtml = currentChildId ? `
                    <div style="text-align: center; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <label style="display: flex; align-items: center; justify-content: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" class="vaccination-checkbox"
                                   ${isCompleted ? 'checked' : ''}
                                   onchange="toggleVaccination(${index})">
                            <span style="font-weight: bold; color: ${isCompleted ? '#28a745' : '#6c757d'};">
                                ${isCompleted ? '✅ تم إنجاز التلقيح' : '⏳ لم يتم إنجاز التلقيح بعد'}
                            </span>
                        </label>

                    </div>
                ` : '';

                vaccinationItem.innerHTML = `
                    <div class="vaccination-age french-text">${vaccination.age}</div>
                    <div class="vaccination-date">${vaccination.formattedDate}</div>
                    ${vaccinesHtml}
                    ${vaccination.additionalInfo ? `<div class="vaccination-vaccines french-text" style="color: #666; font-style: italic; margin-top: 10px;">${vaccination.additionalInfo}</div>` : ''}
                    ${checkboxHtml}
                    <div class="market-dates-section" id="marketDates${index}">
                        <div class="market-dates-title arabic-text">🛒 أيام السوق القريبة:</div>
                        <div class="market-dates-list" id="marketDatesList${index}"></div>
                    </div>
                `;

                vaccinationScheduleDiv.appendChild(vaccinationItem);
            });

            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });

            // تحديث تواريخ السوق إذا كان يوم السوق محدد
            if (marketDay.value) {
                updateMarketDates();
            }
        }

        function updateMarketDates() {
            const selectedDay = parseInt(marketDay.value);

            if (isNaN(selectedDay) || currentVaccinationDates.length === 0) {
                // إخفاء جميع أقسام السوق
                currentVaccinationDates.forEach((_, index) => {
                    const marketSection = document.getElementById(`marketDates${index}`);
                    if (marketSection) {
                        marketSection.classList.remove('show');
                    }
                });
                return;
            }

            currentVaccinationDates.forEach((vaccination, index) => {
                const marketSection = document.getElementById(`marketDates${index}`);
                const marketList = document.getElementById(`marketDatesList${index}`);

                if (marketSection && marketList) {
                    const nearbyMarketDates = findNearbyMarketDates(vaccination.date, selectedDay);

                    if (nearbyMarketDates.length > 0) {
                        marketList.innerHTML = '';
                        nearbyMarketDates.forEach(marketDate => {
                            const dateItem = document.createElement('div');
                            dateItem.className = `market-date-item ${marketDate.isExact ? 'exact-match' : ''}`;
                            dateItem.textContent = marketDate.formattedDate;
                            if (marketDate.isExact) {
                                dateItem.textContent += ' 🎯';
                            }
                            marketList.appendChild(dateItem);
                        });
                        marketSection.classList.add('show');
                    } else {
                        marketSection.classList.remove('show');
                    }
                }
            });
        }

        function findNearbyMarketDates(vaccinationDate, marketDayOfWeek) {
            const nearbyDates = [];
            const vacDate = new Date(vaccinationDate);

            // البحث عن تواريخ السوق قبل وبعد موعد التلقيح (أسبوعين قبل وأسبوعين بعد)
            for (let weekOffset = -2; weekOffset <= 2; weekOffset++) {
                const searchDate = new Date(vacDate);
                searchDate.setDate(searchDate.getDate() + (weekOffset * 7));

                // العثور على يوم السوق في هذا الأسبوع
                const daysToMarket = (marketDayOfWeek - searchDate.getDay() + 7) % 7;
                searchDate.setDate(searchDate.getDate() + daysToMarket);

                const isExact = formatDate(searchDate) === formatDate(vacDate);
                const daysDiff = Math.abs((searchDate - vacDate) / (1000 * 60 * 60 * 24));

                // إضافة التاريخ إذا كان قريب (خلال 10 أيام) أو مطابق تماماً
                if (daysDiff <= 10 || isExact) {
                    nearbyDates.push({
                        date: searchDate,
                        formattedDate: formatDate(searchDate),
                        isExact: isExact,
                        daysDiff: daysDiff
                    });
                }
            }

            // ترتيب حسب القرب من موعد التلقيح
            return nearbyDates.sort((a, b) => a.daysDiff - b.daysDiff);
        }

        async function generatePDF() {
            try {
                downloadPdfBtn.disabled = true;
                downloadPdfBtn.innerHTML = '⏳ جاري إنشاء PDF...';

                const { jsPDF } = window.jspdf;

                // إخفاء العناصر التي لا نريدها في PDF
                pdfDownloadSection.style.visibility = 'hidden';
                const developerInfo = document.querySelector('.developer-info');
                const databaseSection = document.querySelector('.database-section');
                const originalDeveloperVisibility = developerInfo.style.visibility;
                const originalDatabaseVisibility = databaseSection.style.visibility;
                developerInfo.style.visibility = 'hidden';
                databaseSection.style.visibility = 'hidden';

                // انتظار قصير للتأكد من تطبيق التغييرات
                await new Promise(resolve => setTimeout(resolve, 200));

                // التقاط صورة للحاوية الرئيسية
                const container = document.querySelector('.container');

                const canvas = await html2canvas(container, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: true,
                    height: container.scrollHeight,
                    width: container.scrollWidth,
                    scrollX: 0,
                    scrollY: 0
                });

                console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);

                // التحقق من أن الصورة تم التقاطها بنجاح
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('فشل في التقاط الصورة - أبعاد غير صحيحة');
                }

                // إنشاء PDF
                const imgData = canvas.toDataURL('image/png');

                // التحقق من أن البيانات صحيحة
                if (!imgData || imgData === 'data:,') {
                    throw new Error('فشل في تحويل الصورة إلى بيانات');
                }

                const pdf = new jsPDF('p', 'mm', 'a4');

                // حساب أبعاد الصورة للصفحة
                const pdfWidth = pdf.internal.pageSize.getWidth(); // 210mm
                const pdfHeight = pdf.internal.pageSize.getHeight(); // 297mm

                console.log('PDF dimensions:', pdfWidth, 'x', pdfHeight);
                console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);

                // حساب نسبة العرض إلى الطول للصورة الأصلية
                const imgAspectRatio = canvas.width / canvas.height;
                console.log('Image aspect ratio:', imgAspectRatio);

                // حساب الأبعاد للحفاظ على النسبة وضمان ظهور الصورة كاملة
                let finalWidth, finalHeight, x, y;

                // حساب الأبعاد بناءً على العرض الكامل
                const widthBasedHeight = pdfWidth / imgAspectRatio;

                // حساب الأبعاد بناءً على الطول الكامل
                const heightBasedWidth = pdfHeight * imgAspectRatio;

                if (widthBasedHeight <= pdfHeight) {
                    // الصورة تدخل في الصفحة عند استخدام العرض الكامل
                    finalWidth = pdfWidth;
                    finalHeight = widthBasedHeight;
                    x = 0;
                    y = (pdfHeight - finalHeight) / 2;
                    console.log('Using full width approach');
                } else {
                    // الصورة طويلة جداً، استخدم الطول الكامل
                    finalWidth = heightBasedWidth;
                    finalHeight = pdfHeight;
                    x = (pdfWidth - finalWidth) / 2;
                    y = 0;
                    console.log('Using full height approach');
                }

                console.log('Final dimensions:', finalWidth, 'x', finalHeight);
                console.log('Position:', x, ',', y);

                console.log('Position:', x, ',', y);

                // إضافة الصورة لتملأ الصفحة بالكامل
                pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

                // حفظ الملف مع اسم الطفل
                const childName = childNameInput.value.trim() || 'طفل';
                const birthDate = birthDateInput.value.replace(/\//g, '-');
                const fileName = `vaccination-schedule-${childName}-${birthDate}.pdf`;
                pdf.save(fileName);

                console.log('PDF تم إنشاؤه بنجاح');

            } catch (error) {
                console.error('خطأ في إنشاء PDF:', error);
                alert(`حدث خطأ أثناء إنشاء ملف PDF: ${error.message}`);
            } finally {
                // إعادة إظهار العناصر المخفية
                pdfDownloadSection.style.visibility = 'visible';
                const developerInfo = document.querySelector('.developer-info');
                const databaseSection = document.querySelector('.database-section');
                developerInfo.style.visibility = originalDeveloperVisibility || 'visible';
                databaseSection.style.visibility = originalDatabaseVisibility || 'visible';

                downloadPdfBtn.disabled = false;
                downloadPdfBtn.innerHTML = '📥 تحميل جدول التلقيح PDF';
            }
        }

        // وظائف إدارة المستخدمين
        function loadUsers() {
            const saved = localStorage.getItem('nursesUsersDB');
            if (saved) {
                usersDatabase = JSON.parse(saved);
            }
        }

        function saveUsers() {
            localStorage.setItem('nursesUsersDB', JSON.stringify(usersDatabase));
        }

        function checkCurrentUser() {
            const saved = localStorage.getItem('currentNurseUser');
            if (saved) {
                currentUser = JSON.parse(saved);
                showUserInfo();
                loadUserDatabase();

                // إعادة تهيئة جميع الصفحات
                setTimeout(() => {
                    initializeVaccineManagement();
                    initializeMedicineManagement();
                    initializeFamilyPlanningManagement();
                }, 500);
            }
        }

        function showUserInfo() {
            document.getElementById('currentUserName').textContent = currentUser.name;
            document.getElementById('currentCenter').textContent = currentUser.center;
            document.getElementById('currentRegion').textContent = currentUser.region;
            document.getElementById('userInfo').style.display = 'block';
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('sidebarMenu').style.display = 'block';
            document.getElementById('miniSidebarPages').style.display = 'block';
        }

        function register() {
            const name = document.getElementById('registerName').value.trim();
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const center = document.getElementById('registerCenter').value.trim();
            const region = document.getElementById('registerRegion').value.trim();

            if (!name || !username || !password || !center || !region) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // التحقق من وجود اسم المستخدم
            if (usersDatabase.find(user => user.username === username)) {
                alert('اسم المستخدم موجود مسبقاً');
                return;
            }

            const newUser = {
                id: Date.now().toString(),
                name: name,
                username: username,
                password: password, // في التطبيق الحقيقي يجب تشفير كلمة المرور
                center: center,
                region: region,
                createdAt: new Date().toISOString()
            };

            usersDatabase.push(newUser);
            saveUsers();

            alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول');
            showLoginForm();

            // مسح الحقول
            document.getElementById('registerName').value = '';
            document.getElementById('registerUsername').value = '';
            document.getElementById('registerPassword').value = '';
            document.getElementById('registerCenter').value = '';
            document.getElementById('registerRegion').value = '';
        }

        function login() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!username || !password) {
                alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            const user = usersDatabase.find(u => u.username === username && u.password === password);

            if (!user) {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
                return;
            }

            currentUser = user;
            localStorage.setItem('currentNurseUser', JSON.stringify(currentUser));
            showUserInfo();
            loadUserDatabase();

            // إعادة تهيئة جميع الصفحات
            setTimeout(() => {
                loadVaccineList();
                initializeVaccineManagement();
                initializeMedicineManagement();
                initializeFamilyPlanningManagement();
            }, 500);

            closeSidebar();

            alert(`مرحباً بك ${user.name}!`);

            // مسح الحقول
            document.getElementById('loginUsername').value = '';
            document.getElementById('loginPassword').value = '';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentUser = null;
                localStorage.removeItem('currentNurseUser');

                // مسح قاعدة بيانات الأطفال الحالية
                childrenDatabase = [];
                displayChildrenList();

                // إخفاء النتائج
                resultsSection.style.display = 'none';
                childNameInput.value = '';
                birthDateInput.value = '';

                // إظهار نموذج تسجيل الدخول
                document.getElementById('userInfo').style.display = 'none';
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('registerForm').style.display = 'none';
                document.getElementById('sidebarMenu').style.display = 'none';
                document.getElementById('miniSidebarPages').style.display = 'none';

                alert('تم تسجيل الخروج بنجاح');
            }
        }

        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
        }

        function showRegisterForm() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        function toggleSidebar() {
            const isOpen = sidebar.classList.contains('open');
            const container = document.querySelector('.container');
            const registryPage = document.getElementById('childrenRegistryPage');
            const dashboardPage = document.getElementById('dashboardPage');
            const vaccineManagementPage = document.getElementById('vaccineManagementPage');
            const medicineManagementPage = document.getElementById('medicineManagementPage');
            const familyPlanningPage = document.getElementById('familyPlanningPage');

            if (isOpen) {
                // إغلاق الشريط الكامل وإظهار المصغر
                sidebar.classList.remove('open');
                overlay.classList.remove('active');
                container.classList.remove('sidebar-open');
                registryPage.classList.remove('sidebar-open');
                dashboardPage.classList.remove('sidebar-open');
                vaccineManagementPage.classList.remove('sidebar-open');
                medicineManagementPage.classList.remove('sidebar-open');
                familyPlanningPage.classList.remove('sidebar-open');
                miniSidebar.classList.remove('hidden');
            } else {
                // فتح الشريط الكامل وإخفاء المصغر
                sidebar.classList.add('open');
                overlay.classList.add('active');
                container.classList.add('sidebar-open');
                registryPage.classList.add('sidebar-open');
                dashboardPage.classList.add('sidebar-open');
                vaccineManagementPage.classList.add('sidebar-open');
                medicineManagementPage.classList.add('sidebar-open');
                familyPlanningPage.classList.add('sidebar-open');
                miniSidebar.classList.add('hidden');
            }
        }

        function closeSidebar() {
            const container = document.querySelector('.container');
            const registryPage = document.getElementById('childrenRegistryPage');
            const dashboardPage = document.getElementById('dashboardPage');
            const vaccineManagementPage = document.getElementById('vaccineManagementPage');
            const medicineManagementPage = document.getElementById('medicineManagementPage');
            const familyPlanningPage = document.getElementById('familyPlanningPage');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            container.classList.remove('sidebar-open');
            registryPage.classList.remove('sidebar-open');
            dashboardPage.classList.remove('sidebar-open');
            vaccineManagementPage.classList.remove('sidebar-open');
            medicineManagementPage.classList.remove('sidebar-open');
            familyPlanningPage.classList.remove('sidebar-open');
            miniSidebar.classList.remove('hidden');
        }

        // تهيئة الحالة الأولية
        function initializeSidebar() {
            miniSidebar.classList.remove('hidden');
        }

        // وظائف التنقل بين الصفحات
        function showMainPage() {
            document.getElementById('mainPage').style.display = 'block';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('medicineManagementPage').style.display = 'none';
            document.getElementById('familyPlanningPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.sidebar-menu-item').classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelector('.mini-page-icon').classList.add('active');

            closeSidebar();
        }

        function showDashboard() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'block';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('medicineManagementPage').style.display = 'none';
            document.getElementById('familyPlanningPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[1].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[1].classList.add('active');

            // تهيئة لوحة القيادة الجديدة
            console.log('عرض لوحة القيادة...');
            setTimeout(() => {
                initializeDashboard();
            }, 100);

            closeSidebar();
        }

        function showVaccineManagement() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'block';
            document.getElementById('medicineManagementPage').style.display = 'none';
            document.getElementById('familyPlanningPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[2].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[2].classList.add('active');

            // تحديث صفحة تدبير اللقاحات
            initializeVaccineManagement();

            closeSidebar();
        }

        function showMedicineManagement() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('medicineManagementPage').style.display = 'block';
            document.getElementById('familyPlanningPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[3].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[3].classList.add('active');

            // تحديث صفحة تدبير الأدوية
            initializeMedicineManagement();

            closeSidebar();
        }

        function showFamilyPlanningManagement() {
            console.log('🔄 فتح صفحة تنظيم الأسرة...');

            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('medicineManagementPage').style.display = 'none';
            document.getElementById('familyPlanningPage').style.display = 'block';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[4].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[4].classList.add('active');

            // تحديث صفحة تنظيم الأسرة مع تأخير
            setTimeout(() => {
                initializeFamilyPlanningManagement();

                // تحديث إضافي للتأكد
                setTimeout(() => {
                    console.log('🔄 تحديث إضافي للشبكات...');
                    if (contraceptiveList.length > 0) {
                        displayFamilyPlanningMonthlyGrids();
                    }
                }, 500);
            }, 100);

            closeSidebar();
        }

        function showChildrenRegistry() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('medicineManagementPage').style.display = 'none';
            document.getElementById('familyPlanningPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'block';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[5].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[5].classList.add('active');

            // تحديث شبكة الأطفال
            updateChildrenGrid();

            closeSidebar();
        }

        // وظائف تنظيم الأسرة
        let contraceptiveList = [];
        let contraceptiveStock = {};
        let familyPlanningMonthlyPlanning = {
            month1: { name: '', contraceptives: {} },
            month2: { name: '', contraceptives: {} },
            month3: { name: '', contraceptives: {} }
        };

        function initializeFamilyPlanningManagement() {
            console.log('🚀 بدء تهيئة إدارة تنظيم الأسرة...');

            if (!currentUser) {
                console.log('⚠️ currentUser غير معرف، سيتم إعادة المحاولة لاحقاً');
                setTimeout(() => {
                    if (currentUser) {
                        console.log('✅ تم العثور على currentUser، إعادة تهيئة...');
                        initializeFamilyPlanningManagement();
                    }
                }, 1000);
                return;
            }

            console.log('👤 المستخدم الحالي:', currentUser.name);

            // تحميل البيانات
            loadContraceptiveList();
            loadFamilyPlanningMonthlyPlanning();

            console.log('📋 عدد التقنيات المحملة:', contraceptiveList.length);

            // عرض البيانات
            displayContraceptiveList();
            initializeFamilyPlanningDefaultMonthNames();

            // تأخير قصير لضمان تحديث DOM
            setTimeout(() => {
                displayFamilyPlanningMonthlyGrids();
                calculateFamilyPlanningTotalStock();
                displayFamilyPlanningStockGrid();

                console.log('✅ تم تهيئة إدارة تنظيم الأسرة بنجاح');
            }, 200);
        }

        function loadContraceptiveList() {
            if (!currentUser) {
                contraceptiveList = [];
                return;
            }

            const savedContraceptives = localStorage.getItem(`contraceptiveList_${currentUser.id}`);
            if (savedContraceptives) {
                contraceptiveList = JSON.parse(savedContraceptives);
            } else {
                contraceptiveList = [];
            }
        }

        function saveContraceptiveList() {
            if (!currentUser) return;
            localStorage.setItem(`contraceptiveList_${currentUser.id}`, JSON.stringify(contraceptiveList));
        }

        function addContraceptive() {
            const nameInput = document.getElementById('contraceptiveName');
            const name = nameInput.value.trim();

            if (!name) {
                alert('يرجى إدخال اسم التقنية');
                return;
            }

            if (!currentUser) {
                alert('يرجى تسجيل الدخول أولاً');
                return;
            }

            // التحقق من عدم وجود التقنية مسبقاً
            if (contraceptiveList.some(contraceptive => contraceptive.name.toLowerCase() === name.toLowerCase())) {
                alert('هذه التقنية موجودة بالفعل في القائمة');
                return;
            }

            const contraceptive = {
                id: Date.now().toString(),
                name: name
            };

            console.log('إضافة تقنية جديدة:', contraceptive);

            contraceptiveList.push(contraceptive);
            saveContraceptiveList();

            // إضافة التقنية للتخطيط الشهري
            familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] = 0;
            familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] = 0;
            familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] = 0;

            // حفظ التخطيط الشهري المحدث
            localStorage.setItem(`familyPlanningMonthlyPlanning_${currentUser.id}`, JSON.stringify(familyPlanningMonthlyPlanning));

            console.log('قائمة التقنيات الحالية:', contraceptiveList);
            console.log('التخطيط الشهري:', familyPlanningMonthlyPlanning);

            // تحديث العرض فوراً
            displayContraceptiveList();
            forceUpdateMonthlyGrids();

            // مسح الحقل
            nameInput.value = '';

            addNotification(`تم إضافة تقنية منع الحمل ${name} بنجاح`, 'success');
        }

        function forceUpdateMonthlyGrids() {
            console.log('إجبار تحديث الشبكات الشهرية...');

            // تأخير قصير للتأكد من تحديث DOM
            setTimeout(() => {
                displayFamilyPlanningMonthlyGrids();
                calculateFamilyPlanningTotalStock();
                displayFamilyPlanningStockGrid();

                // تحديث إضافي للتأكد
                setTimeout(() => {
                    displayFamilyPlanningMonthlyGrids();
                }, 200);
            }, 50);
        }

        function addDefaultContraceptives() {
            if (!currentUser) {
                alert('يرجى تسجيل الدخول أولاً');
                return;
            }

            const defaultContraceptives = [
                "حبوب منع الحمل",
                "اللولب النحاسي",
                "حقنة ديبو",
                "الواقي الذكري",
                "كريم منع الحمل",
                "لصقة منع الحمل"
            ];

            console.log('إضافة التقنيات الافتراضية...');

            let addedCount = 0;
            defaultContraceptives.forEach((defaultName, index) => {
                // التحقق من عدم وجود التقنية مسبقاً
                if (!contraceptiveList.some(existing => existing.name.toLowerCase() === defaultName.toLowerCase())) {
                    const contraceptive = {
                        id: `default_${Date.now()}_${index}`,
                        name: defaultName
                    };

                    console.log(`إضافة تقنية افتراضية: ${defaultName}`);
                    contraceptiveList.push(contraceptive);

                    // إضافة للتخطيط الشهري
                    familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] = 0;
                    familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] = 0;
                    familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] = 0;

                    addedCount++;
                }
            });

            if (addedCount > 0) {
                console.log(`تم إضافة ${addedCount} تقنية افتراضية`);

                saveContraceptiveList();

                // حفظ التخطيط الشهري المحدث
                localStorage.setItem(`familyPlanningMonthlyPlanning_${currentUser.id}`, JSON.stringify(familyPlanningMonthlyPlanning));

                console.log('قائمة التقنيات بعد الإضافة:', contraceptiveList);

                // تحديث العرض
                displayContraceptiveList();
                forceUpdateMonthlyGrids();

                addNotification(`تم إضافة ${addedCount} تقنية منع حمل افتراضية`, 'success');
            } else {
                addNotification('جميع التقنيات الافتراضية موجودة بالفعل', 'info');
            }
        }

        function deleteContraceptive(contraceptiveId) {
            const contraceptive = contraceptiveList.find(item => item.id === contraceptiveId);
            if (!contraceptive) return;

            if (confirm(`هل أنت متأكد من حذف تقنية منع الحمل "${contraceptive.name}"؟`)) {
                contraceptiveList = contraceptiveList.filter(item => item.id !== contraceptiveId);

                // حذف من التخطيط الشهري
                delete familyPlanningMonthlyPlanning.month1.contraceptives[contraceptiveId];
                delete familyPlanningMonthlyPlanning.month2.contraceptives[contraceptiveId];
                delete familyPlanningMonthlyPlanning.month3.contraceptives[contraceptiveId];

                saveContraceptiveList();
                displayContraceptiveList();
                displayFamilyPlanningMonthlyGrids();
                calculateFamilyPlanningTotalStock();
                displayFamilyPlanningStockGrid();

                addNotification(`تم حذف تقنية منع الحمل ${contraceptive.name}`, 'info');
            }
        }

        function displayContraceptiveList() {
            const contraceptiveListDiv = document.getElementById('contraceptiveList');

            if (contraceptiveList.length === 0) {
                contraceptiveListDiv.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد تقنيات منع حمل مضافة بعد</p>';
                return;
            }

            contraceptiveListDiv.innerHTML = '';
            contraceptiveList.forEach(contraceptive => {
                const contraceptiveItem = document.createElement('div');
                contraceptiveItem.className = 'contraceptive-item';
                contraceptiveItem.innerHTML = `
                    <div>
                        <div class="contraceptive-name">${contraceptive.name}</div>
                    </div>
                    <button class="delete-contraceptive-btn" onclick="deleteContraceptive('${contraceptive.id}')">حذف</button>
                `;
                contraceptiveListDiv.appendChild(contraceptiveItem);
            });
        }

        function displayFamilyPlanningMonthlyGrids() {
            console.log('=== بدء تحديث الشبكات الشهرية ===');
            console.log('عدد التقنيات المتاحة:', contraceptiveList.length);
            console.log('قائمة التقنيات:', contraceptiveList);

            const monthConfigs = [
                { id: 'familyPlanningMonth1Grid', name: 'month1', title: 'الشهر الأول' },
                { id: 'familyPlanningMonth2Grid', name: 'month2', title: 'الشهر الثاني' },
                { id: 'familyPlanningMonth3Grid', name: 'month3', title: 'الشهر الثالث' }
            ];

            monthConfigs.forEach((monthConfig, index) => {
                console.log(`--- تحديث ${monthConfig.title} ---`);

                const grid = document.getElementById(monthConfig.id);
                console.log(`البحث عن العنصر: ${monthConfig.id}`);
                if (!grid) {
                    console.error(`❌ لم يتم العثور على العنصر: ${monthConfig.id}`);
                    return;
                }

                console.log(`✅ تم العثور على عنصر ${monthConfig.name}`);

                // مسح المحتوى السابق
                grid.innerHTML = '';

                if (contraceptiveList.length === 0) {
                    grid.innerHTML = `
                        <div style="text-align: center; color: #6c757d; padding: 30px; border: 2px dashed #ddd; border-radius: 8px; background: #f9f9f9;">
                            <h4 style="margin: 0 0 10px 0;">لا توجد تقنيات منع حمل مضافة بعد</h4>
                            <p style="margin: 0;">أضف تقنية أولاً من القسم أعلاه</p>
                        </div>
                    `;
                    console.log(`📝 عرض رسالة فارغة في ${monthConfig.name}`);
                    return;
                }

                console.log(`📋 عرض ${contraceptiveList.length} تقنية في ${monthConfig.name}`);

                contraceptiveList.forEach((contraceptive, contraceptiveIndex) => {
                    console.log(`  - إضافة تقنية ${contraceptiveIndex + 1}: ${contraceptive.name}`);

                    // التأكد من وجود البيانات في التخطيط الشهري
                    if (!familyPlanningMonthlyPlanning[monthConfig.name]) {
                        familyPlanningMonthlyPlanning[monthConfig.name] = { name: '', contraceptives: {} };
                    }
                    if (!familyPlanningMonthlyPlanning[monthConfig.name].contraceptives) {
                        familyPlanningMonthlyPlanning[monthConfig.name].contraceptives = {};
                    }
                    if (!familyPlanningMonthlyPlanning[monthConfig.name].contraceptives[contraceptive.id]) {
                        familyPlanningMonthlyPlanning[monthConfig.name].contraceptives[contraceptive.id] = 0;
                    }

                    const currentValue = familyPlanningMonthlyPlanning[monthConfig.name].contraceptives[contraceptive.id];

                    const contraceptiveItem = document.createElement('div');
                    contraceptiveItem.className = 'contraceptive-month-item';
                    contraceptiveItem.style.cssText = `
                        margin-bottom: 15px;
                        padding: 20px;
                        border: 3px solid #e91e63;
                        border-radius: 10px;
                        background: linear-gradient(135deg, #fff 0%, #fce4ec 100%);
                        box-shadow: 0 2px 8px rgba(233, 30, 99, 0.1);
                    `;

                    contraceptiveItem.innerHTML = `
                        <div style="margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #e91e63; font-size: 1.2em; font-weight: bold; text-align: center;">
                                🔹 ${contraceptive.name}
                            </h4>
                        </div>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 15px; flex-wrap: wrap;">
                            <label style="font-weight: bold; color: #333; font-size: 1.1em;">العدد المطلوب:</label>
                            <input type="number"
                                   class="contraceptive-quantity-input"
                                   id="familyPlanning${monthConfig.name}_${contraceptive.id}"
                                   value="${currentValue}"
                                   min="0"
                                   max="10000"
                                   style="
                                       width: 120px;
                                       padding: 12px;
                                       border: 3px solid #e91e63;
                                       border-radius: 8px;
                                       text-align: center;
                                       font-size: 1.2em;
                                       font-weight: bold;
                                       background: #fff;
                                   "
                                   onchange="updateFamilyPlanningMonthlyQuantity('${monthConfig.name}', '${contraceptive.id}')"
                                   onfocus="this.style.borderColor='#c2185b'"
                                   onblur="this.style.borderColor='#e91e63'">
                            <span style="color: #666; font-size: 1em; font-weight: bold;">عدد</span>
                        </div>
                    `;

                    grid.appendChild(contraceptiveItem);
                    console.log(`    ✅ تم إضافة ${contraceptive.name} إلى ${monthConfig.name}`);
                });

                console.log(`✅ تم الانتهاء من تحديث ${monthConfig.title}`);
            });

            console.log('=== انتهاء تحديث الشبكات الشهرية ===');
        }

        function refreshFamilyPlanningGrids() {
            console.log('🔄 إعادة تحديث شبكات تنظيم الأسرة...');
            console.log('عدد التقنيات الحالية:', contraceptiveList.length);

            if (contraceptiveList.length === 0) {
                console.log('⚠️ لا توجد تقنيات لعرضها');
                addNotification('لا توجد تقنيات منع حمل لعرضها. أضف تقنية أولاً.', 'warning');
                return;
            }

            // إجبار تحديث فوري
            forceUpdateMonthlyGrids();

            addNotification('تم تحديث شبكات تنظيم الأسرة بنجاح', 'success');
        }

        function updateFamilyPlanningMonthlyQuantity(month, contraceptiveId) {
            const input = document.getElementById(`familyPlanning${month}_${contraceptiveId}`);
            const value = parseInt(input.value) || 0;

            familyPlanningMonthlyPlanning[month].contraceptives[contraceptiveId] = value;

            // إعادة حساب المجموع الإجمالي
            calculateFamilyPlanningTotalStock();
            displayFamilyPlanningStockGrid();
        }

        function calculateFamilyPlanningTotalStock() {
            contraceptiveStock = {};

            // جمع كميات الأشهر الثلاثة
            contraceptiveList.forEach(contraceptive => {
                const month1Qty = familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] || 0;
                const month2Qty = familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] || 0;
                const month3Qty = familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] || 0;

                contraceptiveStock[contraceptive.id] = month1Qty + month2Qty + month3Qty;
            });
        }

        function displayFamilyPlanningStockGrid() {
            const stockGrid = document.getElementById('familyPlanningStockGrid');
            if (!stockGrid) return;

            stockGrid.innerHTML = '';

            if (contraceptiveList.length === 0) {
                stockGrid.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد تقنيات منع حمل مضافة بعد</p>';
                return;
            }

            contraceptiveList.forEach(contraceptive => {
                const totalStock = contraceptiveStock[contraceptive.id] || 0;
                const month1Qty = familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] || 0;
                const month2Qty = familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] || 0;
                const month3Qty = familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] || 0;

                const stockItem = document.createElement('div');
                stockItem.className = 'stock-item';
                stockItem.innerHTML = `
                    <h4>${contraceptive.name}</h4>
                    <div class="stock-display" style="font-size: 1.3rem; color: #e91e63; font-weight: bold;">
                        المجموع: ${totalStock} عدد
                    </div>
                    <div style="font-size: 0.9em; color: #6c757d; margin: 8px 0;">
                        <div>📅 ${familyPlanningMonthlyPlanning.month1.name || 'الشهر الأول'}: ${month1Qty}</div>
                        <div>📅 ${familyPlanningMonthlyPlanning.month2.name || 'الشهر الثاني'}: ${month2Qty}</div>
                        <div>📅 ${familyPlanningMonthlyPlanning.month3.name || 'الشهر الثالث'}: ${month3Qty}</div>
                    </div>
                `;
                stockGrid.appendChild(stockItem);
            });
        }

        function loadFamilyPlanningMonthlyPlanning() {
            if (!currentUser) {
                familyPlanningMonthlyPlanning = {
                    month1: { name: '', contraceptives: {} },
                    month2: { name: '', contraceptives: {} },
                    month3: { name: '', contraceptives: {} }
                };
                return;
            }

            const savedPlanning = localStorage.getItem(`familyPlanningMonthlyPlanning_${currentUser.id}`);
            if (savedPlanning) {
                familyPlanningMonthlyPlanning = JSON.parse(savedPlanning);

                // تحديث أسماء الأشهر في الواجهة
                const month1Input = document.getElementById('familyPlanningMonth1Name');
                const month2Input = document.getElementById('familyPlanningMonth2Name');
                const month3Input = document.getElementById('familyPlanningMonth3Name');

                if (month1Input) month1Input.value = familyPlanningMonthlyPlanning.month1.name || '';
                if (month2Input) month2Input.value = familyPlanningMonthlyPlanning.month2.name || '';
                if (month3Input) month3Input.value = familyPlanningMonthlyPlanning.month3.name || '';
            } else {
                // تهيئة التخطيط الافتراضي
                familyPlanningMonthlyPlanning = {
                    month1: { name: '', contraceptives: {} },
                    month2: { name: '', contraceptives: {} },
                    month3: { name: '', contraceptives: {} }
                };
            }
        }

        function saveFamilyPlanningMonthlyPlanning() {
            if (!currentUser) return;

            // حفظ أسماء الأشهر
            familyPlanningMonthlyPlanning.month1.name = document.getElementById('familyPlanningMonth1Name').value;
            familyPlanningMonthlyPlanning.month2.name = document.getElementById('familyPlanningMonth2Name').value;
            familyPlanningMonthlyPlanning.month3.name = document.getElementById('familyPlanningMonth3Name').value;

            // حفظ كميات تقنيات منع الحمل
            ['month1', 'month2', 'month3'].forEach(month => {
                contraceptiveList.forEach(contraceptive => {
                    const input = document.getElementById(`familyPlanning${month}_${contraceptive.id}`);
                    if (input) {
                        familyPlanningMonthlyPlanning[month].contraceptives[contraceptive.id] = parseInt(input.value) || 0;
                    }
                });
            });

            localStorage.setItem(`familyPlanningMonthlyPlanning_${currentUser.id}`, JSON.stringify(familyPlanningMonthlyPlanning));

            // إعادة حساب المجموع الإجمالي
            calculateFamilyPlanningTotalStock();
            displayFamilyPlanningStockGrid();

            addNotification('تم حفظ التخطيط الشهري لتنظيم الأسرة بنجاح', 'success');
        }

        function resetFamilyPlanningMonthlyPlanning() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع بيانات تنظيم الأسرة؟')) {
                familyPlanningMonthlyPlanning = {
                    month1: { name: '', contraceptives: {} },
                    month2: { name: '', contraceptives: {} },
                    month3: { name: '', contraceptives: {} }
                };

                // إعادة تهيئة تقنيات منع الحمل
                contraceptiveList.forEach(contraceptive => {
                    familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] = 0;
                    familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] = 0;
                    familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] = 0;
                });

                // مسح أسماء الأشهر
                document.getElementById('familyPlanningMonth1Name').value = '';
                document.getElementById('familyPlanningMonth2Name').value = '';
                document.getElementById('familyPlanningMonth3Name').value = '';

                displayFamilyPlanningMonthlyGrids();
                calculateFamilyPlanningTotalStock();
                displayFamilyPlanningStockGrid();

                addNotification('تم إعادة تعيين التخطيط الشهري لتنظيم الأسرة', 'info');
            }
        }

        function initializeFamilyPlanningDefaultMonthNames() {
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // تعيين أسماء الأشهر الافتراضية
            if (!familyPlanningMonthlyPlanning.month1.name) {
                const month1Index = now.getMonth();
                const month1Input = document.getElementById('familyPlanningMonth1Name');
                if (month1Input) {
                    month1Input.value = monthNames[month1Index];
                    familyPlanningMonthlyPlanning.month1.name = monthNames[month1Index];
                }
            }

            if (!familyPlanningMonthlyPlanning.month2.name) {
                const month2Index = (now.getMonth() + 1) % 12;
                const month2Input = document.getElementById('familyPlanningMonth2Name');
                if (month2Input) {
                    month2Input.value = monthNames[month2Index];
                    familyPlanningMonthlyPlanning.month2.name = monthNames[month2Index];
                }
            }

            if (!familyPlanningMonthlyPlanning.month3.name) {
                const month3Index = (now.getMonth() + 2) % 12;
                const month3Input = document.getElementById('familyPlanningMonth3Name');
                if (month3Input) {
                    month3Input.value = monthNames[month3Index];
                    familyPlanningMonthlyPlanning.month3.name = monthNames[month3Index];
                }
            }
        }

        function downloadFamilyPlanningStockReport() {
            // إنشاء محتوى التقرير
            const reportContent = generateFamilyPlanningStockReportHTML();

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportContent);
            printWindow.document.close();

            // طباعة التقرير كـ PDF
            setTimeout(() => {
                printWindow.print();
            }, 500);
        }

        function generateFamilyPlanningStockReportHTML() {
            const now = new Date();
            const reportDate = now.toLocaleDateString('ar-MA');
            const centerName = currentUser ? currentUser.center : 'المركز الصحي';
            const nurseName = currentUser ? currentUser.name : 'الممرض';

            // جدول التخطيط الشهري
            let monthlyPlanningRows = '';
            contraceptiveList.forEach(contraceptive => {
                const month1Qty = familyPlanningMonthlyPlanning.month1.contraceptives[contraceptive.id] || 0;
                const month2Qty = familyPlanningMonthlyPlanning.month2.contraceptives[contraceptive.id] || 0;
                const month3Qty = familyPlanningMonthlyPlanning.month3.contraceptives[contraceptive.id] || 0;
                const total = month1Qty + month2Qty + month3Qty;

                monthlyPlanningRows += `
                    <tr>
                        <td>${contraceptive.name}</td>
                        <td>${month1Qty}</td>
                        <td>${month2Qty}</td>
                        <td>${month3Qty}</td>
                        <td style="font-weight: bold; color: #e91e63;">${total}</td>
                    </tr>
                `;
            });

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير تخطيط تنظيم الأسرة</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background: #f5f5f5; font-weight: bold; }
                        .section-title { color: #e91e63; font-size: 1.2em; margin: 20px 0 10px 0; }
                        .total-column { background: #fce4ec; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير تخطيط تنظيم الأسرة</h1>
                        <h2>${centerName}</h2>
                    </div>

                    <div class="info">
                        <p><strong>الممرض:</strong> ${nurseName}</p>
                        <p><strong>تاريخ التقرير:</strong> ${reportDate}</p>
                    </div>

                    <div class="section-title">التخطيط الشهري لتقنيات منع الحمل</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اسم التقنية</th>
                                <th>${familyPlanningMonthlyPlanning.month1.name || 'الشهر الأول'}</th>
                                <th>${familyPlanningMonthlyPlanning.month2.name || 'الشهر الثاني'}</th>
                                <th>${familyPlanningMonthlyPlanning.month3.name || 'الشهر الثالث'}</th>
                                <th class="total-column">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${monthlyPlanningRows}
                        </tbody>
                    </table>

                    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h3 style="color: #e91e63;">ملاحظات:</h3>
                        <ul>
                            <li>هذا التقرير يعكس التخطيط الشهري لتقنيات منع الحمل</li>
                            <li>المجموع يمثل إجمالي العدد المطلوب للأشهر الثلاثة</li>
                            <li>يُنصح بمراجعة هذا التخطيط شهرياً وتحديثه حسب الحاجة</li>
                            <li>تأكد من توفر تقنيات منع الحمل قبل انتهاء المخزون الحالي</li>
                        </ul>
                    </div>
                </body>
                </html>
            `;
        }

        // وظائف تدبير اللقاحات
        let vaccineList = [];
        let vaccineStock = {};
        let monthlyPlanning = {
            month1: { name: '', vaccines: {} },
            month2: { name: '', vaccines: {} },
            month3: { name: '', vaccines: {} }
        };

        // وظائف تحميل وحفظ اللقاحات
        function loadVaccineList() {
            if (!currentUser) {
                vaccineList = [];
                return;
            }

            const savedVaccines = localStorage.getItem(`vaccineList_${currentUser.id}`);
            if (savedVaccines) {
                vaccineList = JSON.parse(savedVaccines);
            } else {
                // إضافة لقاحات افتراضية
                vaccineList = [
                    { id: 'bcg', name: 'لقاح السل (BCG)' },
                    { id: 'vpo0', name: 'شلل الأطفال عند الولادة (VPO0)' },
                    { id: 'vpo1', name: 'شلل الأطفال الأول (VPO1)' },
                    { id: 'vpo2', name: 'شلل الأطفال الثاني (VPO2)' },
                    { id: 'vpo3', name: 'شلل الأطفال الثالث (VPO3)' },
                    { id: 'penta1', name: 'اللقاح الخماسي الأول (PENTA1)' },
                    { id: 'penta2', name: 'اللقاح الخماسي الثاني (PENTA2)' },
                    { id: 'penta3', name: 'اللقاح الخماسي الثالث (PENTA3)' },
                    { id: 'rr1', name: 'الحصبة والحصبة الألمانية الأول (RR1)' },
                    { id: 'rr2', name: 'الحصبة والحصبة الألمانية الثاني (RR2)' }
                ];
                saveVaccineList();
            }
        }

        function saveVaccineList() {
            if (!currentUser) return;
            localStorage.setItem(`vaccineList_${currentUser.id}`, JSON.stringify(vaccineList));
        }

        // وظائف تدبير الأدوية
        let medicineList = [];
        let medicineStock = {};
        let medicineMonthlyPlanning = {
            month1: { name: '', medicines: {} },
            month2: { name: '', medicines: {} },
            month3: { name: '', medicines: {} }
        };

        function initializeMedicineManagement() {
            loadMedicineList();
            loadMedicineMonthlyPlanning();
            displayMedicineList();
            displayMedicineMonthlyGrids();
            displayMedicineStockGrid();
            initializeMedicineDefaultMonthNames();
        }

        function loadMedicineList() {
            if (!currentUser) return;

            const savedMedicines = localStorage.getItem(`medicineList_${currentUser.id}`);
            if (savedMedicines) {
                medicineList = JSON.parse(savedMedicines);
            } else {
                medicineList = [];
            }
        }

        function saveMedicineList() {
            if (!currentUser) return;
            localStorage.setItem(`medicineList_${currentUser.id}`, JSON.stringify(medicineList));
        }

        function addMedicine() {
            const nameInput = document.getElementById('medicineName');
            const unitInput = document.getElementById('medicineUnit');

            const name = nameInput.value.trim();
            const unit = unitInput.value.trim();

            if (!name || !unit) {
                alert('يرجى إدخال اسم الدواء والوحدة');
                return;
            }

            // التحقق من عدم وجود الدواء مسبقاً
            if (medicineList.some(med => med.name.toLowerCase() === name.toLowerCase())) {
                alert('هذا الدواء موجود بالفعل في القائمة');
                return;
            }

            const medicine = {
                id: Date.now().toString(),
                name: name,
                unit: unit
            };

            medicineList.push(medicine);
            saveMedicineList();

            // إضافة الدواء للتخطيط الشهري
            medicineMonthlyPlanning.month1.medicines[medicine.id] = 0;
            medicineMonthlyPlanning.month2.medicines[medicine.id] = 0;
            medicineMonthlyPlanning.month3.medicines[medicine.id] = 0;

            displayMedicineList();
            displayMedicineMonthlyGrids();

            // مسح الحقول
            nameInput.value = '';
            unitInput.value = '';

            addNotification(`تم إضافة الدواء ${name} بنجاح`, 'success');
        }

        function deleteMedicine(medicineId) {
            const medicine = medicineList.find(med => med.id === medicineId);
            if (!medicine) return;

            if (confirm(`هل أنت متأكد من حذف الدواء "${medicine.name}"؟`)) {
                medicineList = medicineList.filter(med => med.id !== medicineId);

                // حذف من التخطيط الشهري
                delete medicineMonthlyPlanning.month1.medicines[medicineId];
                delete medicineMonthlyPlanning.month2.medicines[medicineId];
                delete medicineMonthlyPlanning.month3.medicines[medicineId];

                saveMedicineList();
                displayMedicineList();
                displayMedicineMonthlyGrids();
                calculateMedicineTotalStock();
                displayMedicineStockGrid();

                addNotification(`تم حذف الدواء ${medicine.name}`, 'info');
            }
        }

        function displayMedicineList() {
            const medicineListDiv = document.getElementById('medicineList');

            if (medicineList.length === 0) {
                medicineListDiv.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أدوية مضافة بعد</p>';
                return;
            }

            medicineListDiv.innerHTML = '';
            medicineList.forEach(medicine => {
                const medicineItem = document.createElement('div');
                medicineItem.className = 'medicine-item';
                medicineItem.innerHTML = `
                    <div>
                        <div class="medicine-name">${medicine.name}</div>
                        <div class="medicine-unit">الوحدة: ${medicine.unit}</div>
                    </div>
                    <button class="delete-medicine-btn" onclick="deleteMedicine('${medicine.id}')">حذف</button>
                `;
                medicineListDiv.appendChild(medicineItem);
            });
        }

        // وظائف تدبير اللقاحات
        let vaccineStock = {};
        let vaccineUsageLog = [];
        let monthlyPlanning = {
            month1: { name: '', vaccines: {} },
            month2: { name: '', vaccines: {} },
            month3: { name: '', vaccines: {} }
        };

        function initializeMedicineDefaultMonthNames() {
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // تعيين أسماء الأشهر الافتراضية
            if (!medicineMonthlyPlanning.month1.name) {
                const month1Index = now.getMonth();
                document.getElementById('medicineMonth1Name').value = monthNames[month1Index];
                medicineMonthlyPlanning.month1.name = monthNames[month1Index];
            }

            if (!medicineMonthlyPlanning.month2.name) {
                const month2Index = (now.getMonth() + 1) % 12;
                document.getElementById('medicineMonth2Name').value = monthNames[month2Index];
                medicineMonthlyPlanning.month2.name = monthNames[month2Index];
            }

            if (!medicineMonthlyPlanning.month3.name) {
                const month3Index = (now.getMonth() + 2) % 12;
                document.getElementById('medicineMonth3Name').value = monthNames[month3Index];
                medicineMonthlyPlanning.month3.name = monthNames[month3Index];
            }
        }

        function loadMedicineMonthlyPlanning() {
            if (!currentUser) return;

            const savedPlanning = localStorage.getItem(`medicineMonthlyPlanning_${currentUser.id}`);
            if (savedPlanning) {
                medicineMonthlyPlanning = JSON.parse(savedPlanning);

                // تحديث أسماء الأشهر في الواجهة
                document.getElementById('medicineMonth1Name').value = medicineMonthlyPlanning.month1.name || '';
                document.getElementById('medicineMonth2Name').value = medicineMonthlyPlanning.month2.name || '';
                document.getElementById('medicineMonth3Name').value = medicineMonthlyPlanning.month3.name || '';
            } else {
                // تهيئة التخطيط الافتراضي
                medicineMonthlyPlanning = {
                    month1: { name: '', medicines: {} },
                    month2: { name: '', medicines: {} },
                    month3: { name: '', medicines: {} }
                };
            }
        }

        function saveMedicineMonthlyPlanning() {
            if (!currentUser) return;

            // حفظ أسماء الأشهر
            medicineMonthlyPlanning.month1.name = document.getElementById('medicineMonth1Name').value;
            medicineMonthlyPlanning.month2.name = document.getElementById('medicineMonth2Name').value;
            medicineMonthlyPlanning.month3.name = document.getElementById('medicineMonth3Name').value;

            // حفظ كميات الأدوية
            ['month1', 'month2', 'month3'].forEach(month => {
                medicineList.forEach(medicine => {
                    const input = document.getElementById(`medicine${month}_${medicine.id}`);
                    if (input) {
                        medicineMonthlyPlanning[month].medicines[medicine.id] = parseInt(input.value) || 0;
                    }
                });
            });

            localStorage.setItem(`medicineMonthlyPlanning_${currentUser.id}`, JSON.stringify(medicineMonthlyPlanning));

            // إعادة حساب المخزون الإجمالي
            calculateMedicineTotalStock();
            displayMedicineStockGrid();

            addNotification('تم حفظ التخطيط الشهري للأدوية بنجاح', 'success');
        }



        function resetMedicineMonthlyPlanning() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع بيانات الأدوية؟')) {
                medicineMonthlyPlanning = {
                    month1: { name: '', medicines: {} },
                    month2: { name: '', medicines: {} },
                    month3: { name: '', medicines: {} }
                };

                // إعادة تهيئة الأدوية
                medicineList.forEach(medicine => {
                    medicineMonthlyPlanning.month1.medicines[medicine.id] = 0;
                    medicineMonthlyPlanning.month2.medicines[medicine.id] = 0;
                    medicineMonthlyPlanning.month3.medicines[medicine.id] = 0;
                });

                // مسح أسماء الأشهر
                document.getElementById('medicineMonth1Name').value = '';
                document.getElementById('medicineMonth2Name').value = '';
                document.getElementById('medicineMonth3Name').value = '';

                displayMedicineMonthlyGrids();
                calculateMedicineTotalStock();
                displayMedicineStockGrid();

                addNotification('تم إعادة تعيين التخطيط الشهري للأدوية', 'info');
            }
        }



        function initializeVaccineManagement() {
            loadVaccineStock();
            loadMonthlyPlanning();
            displayMonthlyGrids();
            displayStockGrid();
            displayNotifications();
            initializeDefaultMonthNames();
        }

        function initializeDefaultMonthNames() {
            const now = new Date();
            const monthNames = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];

            // تعيين أسماء الأشهر الافتراضية
            if (!monthlyPlanning.month1.name) {
                const month1Index = now.getMonth();
                document.getElementById('month1Name').value = monthNames[month1Index];
                monthlyPlanning.month1.name = monthNames[month1Index];
            }

            if (!monthlyPlanning.month2.name) {
                const month2Index = (now.getMonth() + 1) % 12;
                document.getElementById('month2Name').value = monthNames[month2Index];
                monthlyPlanning.month2.name = monthNames[month2Index];
            }

            if (!monthlyPlanning.month3.name) {
                const month3Index = (now.getMonth() + 2) % 12;
                document.getElementById('month3Name').value = monthNames[month3Index];
                monthlyPlanning.month3.name = monthNames[month3Index];
            }
        }

        function loadVaccineStock() {
            if (!currentUser) return;

            const savedStock = localStorage.getItem(`vaccineStock_${currentUser.id}`);
            const savedLog = localStorage.getItem(`vaccineUsageLog_${currentUser.id}`);

            if (savedStock) {
                vaccineStock = JSON.parse(savedStock);
            } else {
                // تهيئة المخزون الافتراضي - سيتم حسابه من التخطيط الشهري
                vaccineStock = {};
            }

            if (savedLog) {
                vaccineUsageLog = JSON.parse(savedLog);
            }
        }

        function loadMonthlyPlanning() {
            if (!currentUser) return;

            const savedPlanning = localStorage.getItem(`monthlyPlanning_${currentUser.id}`);
            if (savedPlanning) {
                monthlyPlanning = JSON.parse(savedPlanning);

                // تحديث أسماء الأشهر في الواجهة
                document.getElementById('month1Name').value = monthlyPlanning.month1.name || '';
                document.getElementById('month2Name').value = monthlyPlanning.month2.name || '';
                document.getElementById('month3Name').value = monthlyPlanning.month3.name || '';
            } else {
                // تهيئة التخطيط الافتراضي
                const defaultVaccines = {
                    'HB1': 0, 'BCG': 0, 'VPO': 0, 'VPI': 0, 'Rota': 0,
                    'Penta': 0, 'RR': 0, 'Pneumo': 0, 'DTC': 0, 'VAT': 0
                };

                monthlyPlanning = {
                    month1: { name: '', vaccines: {...defaultVaccines} },
                    month2: { name: '', vaccines: {...defaultVaccines} },
                    month3: { name: '', vaccines: {...defaultVaccines} }
                };
            }
        }

        function saveMonthlyPlanning() {
            if (!currentUser) return;

            // حفظ أسماء الأشهر
            monthlyPlanning.month1.name = document.getElementById('month1Name').value;
            monthlyPlanning.month2.name = document.getElementById('month2Name').value;
            monthlyPlanning.month3.name = document.getElementById('month3Name').value;

            // حفظ كميات اللقاحات
            ['month1', 'month2', 'month3'].forEach(month => {
                Object.keys(monthlyPlanning[month].vaccines).forEach(vaccine => {
                    const input = document.getElementById(`${month}_${vaccine}`);
                    if (input) {
                        monthlyPlanning[month].vaccines[vaccine] = parseInt(input.value) || 0;
                    }
                });
            });

            localStorage.setItem(`monthlyPlanning_${currentUser.id}`, JSON.stringify(monthlyPlanning));

            // إعادة حساب المخزون الإجمالي
            calculateTotalStock();
            displayStockGrid();

            addNotification('تم حفظ التخطيط الشهري بنجاح', 'success');
        }

        function resetMonthlyPlanning() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع البيانات؟')) {
                const defaultVaccines = {
                    'HB1': 0, 'BCG': 0, 'VPO': 0, 'VPI': 0, 'Rota': 0,
                    'Penta': 0, 'RR': 0, 'Pneumo': 0, 'DTC': 0, 'VAT': 0
                };

                monthlyPlanning = {
                    month1: { name: '', vaccines: {...defaultVaccines} },
                    month2: { name: '', vaccines: {...defaultVaccines} },
                    month3: { name: '', vaccines: {...defaultVaccines} }
                };

                // مسح أسماء الأشهر
                document.getElementById('month1Name').value = '';
                document.getElementById('month2Name').value = '';
                document.getElementById('month3Name').value = '';

                displayMonthlyGrids();
                calculateTotalStock();
                displayStockGrid();

                addNotification('تم إعادة تعيين التخطيط الشهري', 'info');
            }
        }

        function displayMonthlyGrids() {
            const vaccineDefinitions = {
                'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B' },
                'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
                'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)' },
                'VPI': { name: 'شلل الأطفال المحقون', frenchName: 'VPI (Polio Injectable)' },
                'Rota': { name: 'الروتا', frenchName: 'Rotavirus' },
                'Penta': { name: 'الخماسي', frenchName: 'Pentavalent' },
                'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)' },
                'Pneumo': { name: 'المكورات الرئوية', frenchName: 'Pneumocoque' },
                'DTC': { name: 'الثلاثي', frenchName: 'DTC (Diphtérie-Tétanos-Coqueluche)' },
                'VAT': { name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
            };

            ['month1', 'month2', 'month3'].forEach(month => {
                const grid = document.getElementById(`${month}Grid`);
                grid.innerHTML = '';

                Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                    const vaccine = vaccineDefinitions[vaccineKey];
                    const currentValue = monthlyPlanning[month].vaccines[vaccineKey] || 0;

                    const vaccineItem = document.createElement('div');
                    vaccineItem.className = 'stock-item';
                    vaccineItem.style.marginBottom = '10px';
                    vaccineItem.innerHTML = `
                        <h5 style="margin-bottom: 5px; color: #28a745;">${vaccine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; font-style: italic; margin-bottom: 8px;">
                            ${vaccine.frenchName}
                        </div>
                        <div class="stock-controls">
                            <input type="number" class="stock-input" id="${month}_${vaccineKey}"
                                   value="${currentValue}" min="0" max="1000"
                                   onchange="updateMonthlyVaccine('${month}', '${vaccineKey}')">
                            <span style="margin-right: 5px; font-size: 0.9em;">قارورة</span>
                        </div>
                    `;
                    grid.appendChild(vaccineItem);
                });
            });
        }

        function updateMonthlyVaccine(month, vaccineKey) {
            const input = document.getElementById(`${month}_${vaccineKey}`);
            const value = parseInt(input.value) || 0;

            monthlyPlanning[month].vaccines[vaccineKey] = value;

            // إعادة حساب المخزون الإجمالي
            calculateTotalStock();
            displayStockGrid();
        }

        function displayMedicineMonthlyGrids() {
            const monthConfigs = [
                { id: 'medicineMonth1Grid', name: 'month1' },
                { id: 'medicineMonth2Grid', name: 'month2' },
                { id: 'medicineMonth3Grid', name: 'month3' }
            ];

            monthConfigs.forEach(monthConfig => {
                const grid = document.getElementById(monthConfig.id);
                if (!grid) {
                    console.error(`لم يتم العثور على العنصر: ${monthConfig.id}`);
                    return;
                }
                grid.innerHTML = '';

                if (medicineList.length === 0) {
                    grid.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أدوية مضافة بعد</p>';
                    return;
                }

                medicineList.forEach(medicine => {
                    const currentValue = medicineMonthlyPlanning[monthConfig.name].medicines[medicine.id] || 0;

                    const medicineItem = document.createElement('div');
                    medicineItem.className = 'stock-item';
                    medicineItem.style.marginBottom = '10px';
                    medicineItem.innerHTML = `
                        <h5 style="margin-bottom: 5px; color: #17a2b8;">${medicine.name}</h5>
                        <div style="font-size: 0.8em; color: #6c757d; margin-bottom: 8px;">
                            الوحدة: ${medicine.unit}
                        </div>
                        <div class="stock-controls">
                            <input type="number" class="stock-input" id="medicine${monthConfig.name}_${medicine.id}"
                                   value="${currentValue}" min="0" max="10000"
                                   onchange="updateMedicineMonthlyQuantity('${monthConfig.name}', '${medicine.id}')">
                            <span style="margin-right: 5px; font-size: 0.9em;">${medicine.unit}</span>
                        </div>
                    `;
                    grid.appendChild(medicineItem);
                });
            });
        }

        function updateMedicineMonthlyQuantity(month, medicineId) {
            const input = document.getElementById(`medicine${month}_${medicineId}`);
            const value = parseInt(input.value) || 0;

            medicineMonthlyPlanning[month].medicines[medicineId] = value;

            // إعادة حساب المخزون الإجمالي
            calculateMedicineTotalStock();
            displayMedicineStockGrid();
        }

        function calculateMedicineTotalStock() {
            medicineStock = {};

            // جمع كميات الأشهر الثلاثة
            medicineList.forEach(medicine => {
                const month1Qty = medicineMonthlyPlanning.month1.medicines[medicine.id] || 0;
                const month2Qty = medicineMonthlyPlanning.month2.medicines[medicine.id] || 0;
                const month3Qty = medicineMonthlyPlanning.month3.medicines[medicine.id] || 0;

                medicineStock[medicine.id] = month1Qty + month2Qty + month3Qty;
            });
        }

        function displayMedicineStockGrid() {
            const stockGrid = document.getElementById('medicineStockGrid');
            stockGrid.innerHTML = '';

            if (medicineList.length === 0) {
                stockGrid.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أدوية مضافة بعد</p>';
                return;
            }

            medicineList.forEach(medicine => {
                const totalStock = medicineStock[medicine.id] || 0;
                const month1Qty = medicineMonthlyPlanning.month1.medicines[medicine.id] || 0;
                const month2Qty = medicineMonthlyPlanning.month2.medicines[medicine.id] || 0;
                const month3Qty = medicineMonthlyPlanning.month3.medicines[medicine.id] || 0;
                const status = getMedicineStockStatus(totalStock);

                const stockItem = document.createElement('div');
                stockItem.className = 'stock-item';
                stockItem.innerHTML = `
                    <h4>${medicine.name}</h4>
                    <div style="font-size: 0.85em; color: #6c757d; margin-bottom: 5px;">
                        الوحدة: ${medicine.unit}
                    </div>
                    <div class="stock-display" style="font-size: 1.3rem; color: #17a2b8; font-weight: bold;">
                        المجموع: ${totalStock} ${medicine.unit}
                    </div>
                    <div style="font-size: 0.9em; color: #6c757d; margin: 8px 0;">
                        <div>📅 ${medicineMonthlyPlanning.month1.name || 'الشهر الأول'}: ${month1Qty}</div>
                        <div>📅 ${medicineMonthlyPlanning.month2.name || 'الشهر الثاني'}: ${month2Qty}</div>
                        <div>📅 ${medicineMonthlyPlanning.month3.name || 'الشهر الثالث'}: ${month3Qty}</div>
                    </div>
                    <span class="stock-status ${status.class}">${status.text}</span>
                `;
                stockGrid.appendChild(stockItem);
            });
        }

        function getMedicineStockStatus(stock) {
            if (stock >= 100) {
                return { class: 'good', text: 'متوفر' };
            } else if (stock >= 30) {
                return { class: 'warning', text: 'منخفض' };
            } else {
                return { class: 'critical', text: 'نفاد' };
            }
        }

        function downloadMedicineStockReport() {
            // إنشاء محتوى التقرير
            const reportContent = generateMedicineStockReportHTML();

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportContent);
            printWindow.document.close();

            // طباعة التقرير كـ PDF
            setTimeout(() => {
                printWindow.print();
            }, 500);
        }

        function generateMedicineStockReportHTML() {
            const now = new Date();
            const reportDate = now.toLocaleDateString('ar-MA');
            const centerName = currentUser ? currentUser.center : 'المركز الصحي';
            const nurseName = currentUser ? currentUser.name : 'الممرض';

            // جدول التخطيط الشهري
            let monthlyPlanningRows = '';
            medicineList.forEach(medicine => {
                const month1Qty = medicineMonthlyPlanning.month1.medicines[medicine.id] || 0;
                const month2Qty = medicineMonthlyPlanning.month2.medicines[medicine.id] || 0;
                const month3Qty = medicineMonthlyPlanning.month3.medicines[medicine.id] || 0;
                const total = month1Qty + month2Qty + month3Qty;

                monthlyPlanningRows += `
                    <tr>
                        <td>${medicine.name}</td>
                        <td>${medicine.unit}</td>
                        <td>${month1Qty}</td>
                        <td>${month2Qty}</td>
                        <td>${month3Qty}</td>
                        <td style="font-weight: bold; color: #17a2b8;">${total}</td>
                    </tr>
                `;
            });

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير تخطيط مخزون الأدوية</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background: #f5f5f5; font-weight: bold; }
                        .section-title { color: #17a2b8; font-size: 1.2em; margin: 20px 0 10px 0; }
                        .total-column { background: #e3f2fd; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير تخطيط مخزون الأدوية</h1>
                        <h2>${centerName}</h2>
                    </div>

                    <div class="info">
                        <p><strong>الممرض:</strong> ${nurseName}</p>
                        <p><strong>تاريخ التقرير:</strong> ${reportDate}</p>
                    </div>

                    <div class="section-title">التخطيط الشهري لمخزون الأدوية</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اسم الدواء</th>
                                <th>الوحدة</th>
                                <th>${medicineMonthlyPlanning.month1.name || 'الشهر الأول'}</th>
                                <th>${medicineMonthlyPlanning.month2.name || 'الشهر الثاني'}</th>
                                <th>${medicineMonthlyPlanning.month3.name || 'الشهر الثالث'}</th>
                                <th class="total-column">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${monthlyPlanningRows}
                        </tbody>
                    </table>

                    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h3 style="color: #17a2b8;">ملاحظات:</h3>
                        <ul>
                            <li>هذا التقرير يعكس التخطيط الشهري لمخزون الأدوية</li>
                            <li>المجموع يمثل إجمالي الكمية المطلوبة للأشهر الثلاثة</li>
                            <li>يُنصح بمراجعة هذا التخطيط شهرياً وتحديثه حسب الحاجة</li>
                            <li>تأكد من توفر الأدوية قبل انتهاء المخزون الحالي</li>
                        </ul>
                    </div>
                </body>
                </html>
            `;
        }

        function calculateTotalStock() {
            vaccineStock = {};

            // جمع كميات الأشهر الثلاثة
            Object.keys(monthlyPlanning.month1.vaccines).forEach(vaccine => {
                const month1Qty = monthlyPlanning.month1.vaccines[vaccine] || 0;
                const month2Qty = monthlyPlanning.month2.vaccines[vaccine] || 0;
                const month3Qty = monthlyPlanning.month3.vaccines[vaccine] || 0;

                vaccineStock[vaccine] = month1Qty + month2Qty + month3Qty;
            });

            saveVaccineStock();
        }

        function saveVaccineStock() {
            if (!currentUser) return;
            localStorage.setItem(`vaccineStock_${currentUser.id}`, JSON.stringify(vaccineStock));
            localStorage.setItem(`vaccineUsageLog_${currentUser.id}`, JSON.stringify(vaccineUsageLog));
        }

        function displayStockGrid() {
            const stockGrid = document.getElementById('stockGrid');
            stockGrid.innerHTML = '';

            const vaccineDefinitions = {
                'HB1': { name: 'التهاب الكبد ب', frenchName: 'Hépatite B', description: 'جرعة واحدة عند الولادة' },
                'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)', description: 'جرعة واحدة عند الولادة' },
                'VPO': { name: 'شلل الأطفال الفموي', frenchName: 'VPO (Polio Oral)', description: 'جميع الجرعات: 0، 2، 4، 6، 18 شهر، 5 سنوات' },
                'VPI': { name: 'شلل الأطفال المحقون', frenchName: 'VPI (Polio Injectable)', description: 'جميع الجرعات: 2، 4، 6 أشهر' },
                'Rota': { name: 'الروتا', frenchName: 'Rotavirus', description: 'جميع الجرعات: 2، 4 أشهر' },
                'Penta': { name: 'الخماسي', frenchName: 'Pentavalent', description: 'جميع الجرعات: 2، 4، 6 أشهر' },
                'RR': { name: 'الحصبة والحصبة الألمانية', frenchName: 'RR (Rougeole-Rubéole)', description: 'جميع الجرعات: 9، 18 شهر' },
                'Pneumo': { name: 'المكورات الرئوية', frenchName: 'Pneumocoque', description: 'جميع الجرعات: 2، 4، 12 شهر' },
                'DTC': { name: 'الثلاثي', frenchName: 'DTC (Diphtérie-Tétanos-Coqueluche)', description: 'جميع الجرعات: 18 شهر، 5 سنوات' },
                'VAT': { name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)', description: 'للنساء الحوامل' }
            };

            Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                const vaccine = vaccineDefinitions[vaccineKey];
                const totalStock = vaccineStock[vaccineKey] || 0;
                const month1Qty = monthlyPlanning.month1.vaccines[vaccineKey] || 0;
                const month2Qty = monthlyPlanning.month2.vaccines[vaccineKey] || 0;
                const month3Qty = monthlyPlanning.month3.vaccines[vaccineKey] || 0;
                const status = getStockStatus(totalStock);

                const stockItem = document.createElement('div');
                stockItem.className = 'stock-item';
                stockItem.innerHTML = `
                    <h4>${vaccine.name}</h4>
                    <div style="font-size: 0.85em; color: #6c757d; font-style: italic; margin-bottom: 5px;">
                        ${vaccine.frenchName}
                    </div>
                    <div style="font-size: 0.8em; color: #495057; margin-bottom: 10px; line-height: 1.3;">
                        ${vaccine.description}
                    </div>
                    <div class="stock-display" style="font-size: 1.3rem; color: #28a745; font-weight: bold;">
                        المجموع: ${totalStock} قارورة
                    </div>
                    <div style="font-size: 0.9em; color: #6c757d; margin: 8px 0;">
                        <div>📅 ${monthlyPlanning.month1.name || 'الشهر الأول'}: ${month1Qty}</div>
                        <div>📅 ${monthlyPlanning.month2.name || 'الشهر الثاني'}: ${month2Qty}</div>
                        <div>📅 ${monthlyPlanning.month3.name || 'الشهر الثالث'}: ${month3Qty}</div>
                    </div>
                    <span class="stock-status ${status.class}">${status.text}</span>
                `;
                stockGrid.appendChild(stockItem);
            });
        }

        function getStockStatus(stock) {
            if (stock >= 30) {
                return { class: 'good', text: 'متوفر' };
            } else if (stock >= 10) {
                return { class: 'warning', text: 'منخفض' };
            } else {
                return { class: 'critical', text: 'نفاد' };
            }
        }



        function getVaccineBottleType(specificVaccine) {
            // تحويل أسماء اللقاحات المحددة إلى نوع القارورة
            const vaccineMapping = {
                // شلل الأطفال الفموي - جميع الجرعات تستخدم نفس القارورة
                'VPO0': 'VPO', 'VPO1': 'VPO', 'VPO2': 'VPO', 'VPO3': 'VPO',
                'VPO1_18m': 'VPO', 'VPO2_5y': 'VPO',

                // شلل الأطفال المحقون - جميع الجرعات تستخدم نفس القارورة
                'VPI1': 'VPI', 'VPI2': 'VPI', 'VPI3': 'VPI',

                // الروتا - جميع الجرعات تستخدم نفس القارورة
                'Rota1': 'Rota', 'Rota2': 'Rota', 'Rota3': 'Rota',

                // الخماسي - جميع الجرعات تستخدم نفس القارورة
                'Penta1': 'Penta', 'Penta2': 'Penta', 'Penta3': 'Penta',

                // الحصبة والحصبة الألمانية - جميع الجرعات تستخدم نفس القارورة
                'RR1': 'RR', 'RR2': 'RR',

                // المكورات الرئوية - جميع الجرعات تستخدم نفس القارورة
                'Pneumo1': 'Pneumo', 'Pneumo2': 'Pneumo', 'Pneumo3': 'Pneumo',

                // الثلاثي - جميع الجرعات تستخدم نفس القارورة
                'DTC1_18m': 'DTC', 'DTC2_5y': 'DTC',

                // اللقاحات الفردية
                'HB1': 'HB1', 'BCG': 'BCG', 'VAT': 'VAT'
            };

            return vaccineMapping[specificVaccine] || specificVaccine;
        }

        function useVaccine(specificVaccineKey, childName) {
            // تحويل اللقاح المحدد إلى نوع القارورة
            const bottleType = getVaccineBottleType(specificVaccineKey);

            if (vaccineStock[bottleType] > 0) {
                vaccineStock[bottleType]--;

                // تسجيل الاستخدام
                vaccineUsageLog.push({
                    vaccine: bottleType,
                    specificVaccine: specificVaccineKey,
                    childName: childName,
                    date: new Date().toISOString(),
                    displayDate: new Date().toLocaleDateString('ar-MA')
                });

                saveVaccineStock();

                // إضافة إشعار الاستخدام
                const vaccineName = getVaccineName(specificVaccineKey);
                addNotification(`تم استخدام لقاح ${vaccineName} للطفل ${childName}`, 'info');

                // تحديث العرض إذا كانت الصفحة مفتوحة
                if (document.getElementById('vaccineManagementPage').style.display === 'block') {
                    displayStockGrid();
                    displayNotifications();
                    displayStockPrediction();
                }
            }
        }

        function getVaccineName(vaccineKey) {
            const names = {
                // أسماء القوارير الأساسية
                'HB1': 'التهاب الكبد ب', 'BCG': 'السل', 'VPO': 'شلل الأطفال الفموي',
                'VPI': 'شلل الأطفال المحقون', 'Rota': 'الروتا', 'Penta': 'الخماسي',
                'RR': 'الحصبة والحصبة الألمانية', 'Pneumo': 'المكورات الرئوية',
                'DTC': 'الثلاثي', 'VAT': 'الكزاز للحوامل',

                // أسماء الجرعات المحددة
                'VPO0': 'شلل الأطفال الفموي (عند الولادة)', 'VPO1': 'شلل الأطفال الفموي الأول',
                'VPO2': 'شلل الأطفال الفموي الثاني', 'VPO3': 'شلل الأطفال الفموي الثالث',
                'VPO1_18m': 'شلل الأطفال الفموي (18 شهر)', 'VPO2_5y': 'شلل الأطفال الفموي (5 سنوات)',

                'VPI1': 'شلل الأطفال المحقون الأول', 'VPI2': 'شلل الأطفال المحقون الثاني',
                'VPI3': 'شلل الأطفال المحقون الثالث',

                'Rota1': 'الروتا الأول', 'Rota2': 'الروتا الثاني', 'Rota3': 'الروتا الثالث',

                'Penta1': 'الخماسي الأول', 'Penta2': 'الخماسي الثاني', 'Penta3': 'الخماسي الثالث',

                'RR1': 'الحصبة والحصبة الألمانية الأول', 'RR2': 'الحصبة والحصبة الألمانية الثاني',

                'Pneumo1': 'المكورات الرئوية الأول', 'Pneumo2': 'المكورات الرئوية الثاني',
                'Pneumo3': 'المكورات الرئوية الثالث',

                'DTC1_18m': 'الثلاثي الأول (18 شهر)', 'DTC2_5y': 'الثلاثي الثاني (5 سنوات)'
            };
            return names[vaccineKey] || vaccineKey;
        }

        function addNotification(message, type = 'info') {
            const notification = {
                id: Date.now(),
                message: message,
                type: type,
                timestamp: new Date().toLocaleString('ar-MA')
            };

            // إضافة للقائمة (الاحتفاظ بآخر 10 إشعارات)
            if (!window.notifications) window.notifications = [];
            window.notifications.unshift(notification);
            if (window.notifications.length > 10) {
                window.notifications = window.notifications.slice(0, 10);
            }

            displayNotifications();
        }

        function displayNotifications() {
            const notificationsList = document.getElementById('notificationsList');
            if (!window.notifications || window.notifications.length === 0) {
                notificationsList.innerHTML = '<p style="color: #6c757d; text-align: center;">لا توجد إشعارات حالياً</p>';
                return;
            }

            notificationsList.innerHTML = '';
            window.notifications.forEach(notification => {
                const notificationItem = document.createElement('div');
                notificationItem.className = `notification-item ${notification.type}`;
                notificationItem.innerHTML = `
                    <div style="font-weight: bold;">${notification.message}</div>
                    <div style="font-size: 0.85em; color: #6c757d; margin-top: 5px;">
                        ${notification.timestamp}
                    </div>
                `;
                notificationsList.appendChild(notificationItem);
            });
        }

        function displayStockPrediction() {
            const tbody = document.getElementById('predictionTableBody');
            tbody.innerHTML = '';

            // حساب معدل الاستهلاك الشهري لكل لقاح
            const monthlyUsage = calculateMonthlyUsage();

            Object.keys(vaccineStock).forEach(vaccineKey => {
                const currentStock = vaccineStock[vaccineKey];
                const monthlyUse = monthlyUsage[vaccineKey] || 0;

                const month1 = Math.max(0, currentStock - monthlyUse);
                const month2 = Math.max(0, month1 - monthlyUse);
                const month3 = Math.max(0, month2 - monthlyUse);

                const status = month3 <= 5 ? 'نفاد متوقع' : month3 <= 15 ? 'خصاص متوقع' : 'متوفر';
                const statusClass = month3 <= 5 ? 'shortage-highlight' : '';

                const row = document.createElement('tr');
                row.className = statusClass;
                row.innerHTML = `
                    <td>${getVaccineName(vaccineKey)}</td>
                    <td>${currentStock}</td>
                    <td>${month1}</td>
                    <td>${month2}</td>
                    <td>${month3}</td>
                    <td>${status}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function calculateMonthlyUsage() {
            const usage = {};
            const now = new Date();
            const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());

            // تهيئة العدادات لأنواع القوارير
            Object.keys(vaccineStock).forEach(key => {
                usage[key] = 0;
            });

            // حساب الاستخدام في آخر 3 أشهر
            vaccineUsageLog.forEach(log => {
                const logDate = new Date(log.date);
                if (logDate >= threeMonthsAgo) {
                    // استخدام نوع القارورة وليس اللقاح المحدد
                    const bottleType = log.vaccine; // هذا مخزن بالفعل كنوع قارورة
                    usage[bottleType] = (usage[bottleType] || 0) + 1;
                }
            });

            // حساب المتوسط الشهري
            Object.keys(usage).forEach(key => {
                usage[key] = Math.ceil(usage[key] / 3);
            });

            return usage;
        }

        function downloadStockReport() {
            // إنشاء محتوى التقرير
            const reportContent = generateStockReportHTML();

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportContent);
            printWindow.document.close();

            // طباعة التقرير كـ PDF
            setTimeout(() => {
                printWindow.print();
            }, 500);
        }

        function generateStockReportHTML() {
            const now = new Date();
            const reportDate = now.toLocaleDateString('ar-MA');
            const centerName = currentUser ? currentUser.center : 'المركز الصحي';
            const nurseName = currentUser ? currentUser.name : 'الممرض';

            // جدول التخطيط الشهري
            let monthlyPlanningRows = '';
            Object.keys(vaccineStock).forEach(vaccineKey => {
                const month1Qty = monthlyPlanning.month1.vaccines[vaccineKey] || 0;
                const month2Qty = monthlyPlanning.month2.vaccines[vaccineKey] || 0;
                const month3Qty = monthlyPlanning.month3.vaccines[vaccineKey] || 0;
                const total = month1Qty + month2Qty + month3Qty;

                monthlyPlanningRows += `
                    <tr>
                        <td>${getVaccineName(vaccineKey)}</td>
                        <td>${month1Qty}</td>
                        <td>${month2Qty}</td>
                        <td>${month3Qty}</td>
                        <td style="font-weight: bold; color: #28a745;">${total}</td>
                    </tr>
                `;
            });

            // جدول المخزون الإجمالي
            let stockTableRows = '';
            Object.keys(vaccineStock).forEach(vaccineKey => {
                const stock = vaccineStock[vaccineKey];
                const status = getStockStatus(stock);
                stockTableRows += `
                    <tr>
                        <td>${getVaccineName(vaccineKey)}</td>
                        <td>${stock}</td>
                        <td>${status.text}</td>
                    </tr>
                `;
            });

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير تخطيط مخزون اللقاحات</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background: #f5f5f5; font-weight: bold; }
                        .section-title { color: #28a745; font-size: 1.2em; margin: 20px 0 10px 0; }
                        .total-column { background: #e8f5e8; font-weight: bold; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير تخطيط مخزون اللقاحات</h1>
                        <h2>${centerName}</h2>
                    </div>

                    <div class="info">
                        <p><strong>الممرض:</strong> ${nurseName}</p>
                        <p><strong>تاريخ التقرير:</strong> ${reportDate}</p>
                    </div>

                    <div class="section-title">التخطيط الشهري للمخزون</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اللقاح</th>
                                <th>${monthlyPlanning.month1.name || 'الشهر الأول'}</th>
                                <th>${monthlyPlanning.month2.name || 'الشهر الثاني'}</th>
                                <th>${monthlyPlanning.month3.name || 'الشهر الثالث'}</th>
                                <th class="total-column">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${monthlyPlanningRows}
                        </tbody>
                    </table>

                    <div class="section-title">المخزون الإجمالي المطلوب</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اللقاح</th>
                                <th>الكمية الإجمالية</th>
                                <th>حالة التوفر</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stockTableRows}
                        </tbody>
                    </table>

                    <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                        <h3 style="color: #28a745;">ملاحظات:</h3>
                        <ul>
                            <li>هذا التقرير يعكس التخطيط الشهري لمخزون اللقاحات</li>
                            <li>المجموع يمثل إجمالي القوارير المطلوبة للأشهر الثلاثة</li>
                            <li>يُنصح بمراجعة هذا التخطيط شهرياً وتحديثه حسب الحاجة</li>
                        </ul>
                    </div>
                </body>
                </html>
            `;
        }

        // تم نقل وظائف لوحة القيادة الجديدة إلى الأسفل

        function getFilteredChildren(startDate, endDate, period) {
            const allChildren = getAllChildren();
            let filtered = allChildren;

            // تطبيق فلتر الفترة المحددة مسبقاً
            if (period !== 'all') {
                const now = new Date();
                let filterDate = new Date();

                switch(period) {
                    case 'month':
                        filterDate.setMonth(now.getMonth() - 1);
                        break;
                    case 'quarter':
                        filterDate.setMonth(now.getMonth() - 3);
                        break;
                    case 'year':
                        filterDate.setFullYear(now.getFullYear() - 1);
                        break;
                }

                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate >= filterDate;
                });
            }

            // تطبيق فلاتر التاريخ المخصصة
            if (startDate) {
                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate >= new Date(startDate);
                });
            }

            if (endDate) {
                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate <= new Date(endDate);
                });
            }

            return filtered;
        }

        function getAllChildren() {
            const children = [];
            const users = JSON.parse(localStorage.getItem('users') || '[]');

            users.forEach(user => {
                if (user.children) {
                    user.children.forEach(child => {
                        children.push({
                            ...child,
                            userId: user.username
                        });
                    });
                }
            });

            return children;
        }

        function calculateVaccinationStats(children) {
            const stats = {
                totalChildren: children.length,
                expectedBirths: children.length, // يمكن تعديلها حسب البيانات المتوفرة
                vaccines: {}
            };

            // تعريف اللقاحات وأعمارها المستهدفة (حسب الجرعات المحددة)
            const vaccineDefinitions = {
                'HB1': { targetAge: 0, name: 'التهاب الكبد ب (عند الولادة)', frenchName: 'Hépatite B (à la naissance)' },
                'BCG': { targetAge: 0, name: 'السل', frenchName: 'BCG (Tuberculose)' },
                'VPO0': { targetAge: 0, name: 'شلل الأطفال الفموي (عند الولادة)', frenchName: 'VPO0 (Polio oral à la naissance)' },
                'VPO1': { targetAge: 60, name: 'شلل الأطفال الفموي الأول', frenchName: 'VPO1 (Polio oral 1ère dose)' },
                'VPO2': { targetAge: 120, name: 'شلل الأطفال الفموي الثاني', frenchName: 'VPO2 (Polio oral 2ème dose)' },
                'VPO3': { targetAge: 180, name: 'شلل الأطفال الفموي الثالث', frenchName: 'VPO3 (Polio oral 3ème dose)' },
                'VPI1': { targetAge: 60, name: 'شلل الأطفال المحقون الأول', frenchName: 'VPI1 (Polio injectable 1ère dose)' },
                'VPI2': { targetAge: 120, name: 'شلل الأطفال المحقون الثاني', frenchName: 'VPI2 (Polio injectable 2ème dose)' },
                'VPI3': { targetAge: 180, name: 'شلل الأطفال المحقون الثالث', frenchName: 'VPI3 (Polio injectable 3ème dose)' },
                'Rota1': { targetAge: 60, name: 'الروتا الأول', frenchName: 'Rota1 (Rotavirus 1ère dose)' },
                'Rota2': { targetAge: 120, name: 'الروتا الثاني', frenchName: 'Rota2 (Rotavirus 2ème dose)' },
                'Penta1': { targetAge: 60, name: 'الخماسي الأول', frenchName: 'Penta1 (Pentavalent 1ère dose)' },
                'Penta2': { targetAge: 120, name: 'الخماسي الثاني', frenchName: 'Penta2 (Pentavalent 2ème dose)' },
                'Penta3': { targetAge: 180, name: 'الخماسي الثالث', frenchName: 'Penta3 (Pentavalent 3ème dose)' },
                'RR1': { targetAge: 270, name: 'الحصبة والحصبة الألمانية الأول', frenchName: 'RR1 (Rougeole-Rubéole 1ère dose)' },
                'Pneumo1': { targetAge: 60, name: 'المكورات الرئوية الأول', frenchName: 'Pneumo1 (Pneumocoque 1ère dose)' },
                'Pneumo2': { targetAge: 120, name: 'المكورات الرئوية الثاني', frenchName: 'Pneumo2 (Pneumocoque 2ème dose)' },
                'Pneumo3': { targetAge: 365, name: 'المكورات الرئوية الثالث', frenchName: 'Pneumo3 (Pneumocoque 3ème dose)' },
                'RR2': { targetAge: 548, name: 'الحصبة والحصبة الألمانية الثاني', frenchName: 'RR2 (Rougeole-Rubéole 2ème dose)' },
                'DTC1_18m': { targetAge: 548, name: 'الثلاثي الأول (18 شهر)', frenchName: 'DTC1 (Diphtérie-Tétanos-Coqueluche 18 mois)' },
                'VPO1_18m': { targetAge: 548, name: 'شلل الأطفال الأول (18 شهر)', frenchName: 'VPO1 (Polio oral 18 mois)' },
                'DTC2_5y': { targetAge: 1825, name: 'الثلاثي الثاني (5 سنوات)', frenchName: 'DTC2 (Diphtérie-Tétanos-Coqueluche 5 ans)' },
                'VPO2_5y': { targetAge: 1825, name: 'شلل الأطفال الثاني (5 سنوات)', frenchName: 'VPO2 (Polio oral 5 ans)' },
                'VAT': { targetAge: 0, name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
            };

            // حساب إحصائيات كل لقاح
            Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                const vaccine = vaccineDefinitions[vaccineKey];
                const eligibleChildren = children.filter(child => {
                    const ageInDays = (new Date() - new Date(child.birthDate)) / (1000 * 60 * 60 * 24);
                    return ageInDays >= vaccine.targetAge;
                });

                const vaccinatedChildren = eligibleChildren.filter(child => {
                    return child.vaccinations && child.vaccinations[vaccineKey] === true;
                });

                const coverage = eligibleChildren.length > 0 ?
                    (vaccinatedChildren.length / eligibleChildren.length) * 100 : 0;

                stats.vaccines[vaccineKey] = {
                    name: vaccine.name,
                    frenchName: vaccine.frenchName,
                    eligible: eligibleChildren.length,
                    vaccinated: vaccinatedChildren.length,
                    coverage: coverage.toFixed(1),
                    status: coverage >= 95 ? 'ممتاز' : coverage >= 80 ? 'جيد' : coverage >= 60 ? 'متوسط' : 'ضعيف'
                };
            });

            return stats;
        }

        function displayStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = '';

            // إحصائيات عامة
            const generalStats = [
                {
                    title: 'إجمالي الأطفال المسجلين',
                    value: stats.totalChildren,
                    description: 'العدد الكلي للأطفال في قاعدة البيانات',
                    color: '#6f42c1'
                },
                {
                    title: 'متوسط التغطية التلقيحية',
                    value: calculateAverageCoverage(stats) + '%',
                    description: 'المتوسط العام لجميع اللقاحات',
                    color: '#28a745'
                },
                {
                    title: 'اللقاحات المكتملة',
                    value: countCompletedVaccines(stats),
                    description: 'عدد اللقاحات التي حققت تغطية +95%',
                    color: '#17a2b8'
                },
                {
                    title: 'اللقاحات المتأخرة',
                    value: countDelayedVaccines(stats),
                    description: 'عدد اللقاحات التي تحتاج متابعة',
                    color: '#dc3545'
                }
            ];

            generalStats.forEach(stat => {
                const card = createStatCard(stat);
                statsGrid.appendChild(card);
            });
        }

        function calculateAverageCoverage(stats) {
            const coverages = Object.values(stats.vaccines).map(v => parseFloat(v.coverage));
            const average = coverages.reduce((sum, coverage) => sum + coverage, 0) / coverages.length;
            return average.toFixed(1);
        }

        function countCompletedVaccines(stats) {
            return Object.values(stats.vaccines).filter(v => parseFloat(v.coverage) >= 95).length;
        }

        function countDelayedVaccines(stats) {
            return Object.values(stats.vaccines).filter(v => parseFloat(v.coverage) < 80).length;
        }

        function createStatCard(stat) {
            const card = document.createElement('div');
            card.className = 'stat-card';
            card.innerHTML = `
                <h3 style="color: ${stat.color}">${stat.title}</h3>
                <div class="stat-value" style="color: ${stat.color}">${stat.value}</div>
                <div class="stat-description">${stat.description}</div>
            `;
            return card;
        }

        function displayDetailedStats(stats) {
            const tbody = document.getElementById('detailedStatsBody');
            tbody.innerHTML = '';

            Object.entries(stats.vaccines).forEach(([key, vaccine]) => {
                const row = document.createElement('tr');
                const statusColor = getStatusColor(vaccine.status);

                row.innerHTML = `
                    <td>
                        <div class="vaccine-name-container">
                            <div class="arabic-text" style="font-weight: bold; margin-bottom: 3px;">${vaccine.name}</div>
                            <div class="french-text" style="font-size: 0.85em; color: #6c757d; font-style: italic;">${vaccine.frenchName}</div>
                        </div>
                    </td>
                    <td>${vaccine.eligible}</td>
                    <td>${vaccine.vaccinated}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${vaccine.coverage}%; background: ${statusColor}">
                                ${vaccine.coverage}%
                            </div>
                        </div>
                    </td>
                    <td><span style="color: ${statusColor}; font-weight: bold">${vaccine.status}</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStatusColor(status) {
            switch(status) {
                case 'ممتاز': return '#28a745';
                case 'جيد': return '#17a2b8';
                case 'متوسط': return '#ffc107';
                case 'ضعيف': return '#dc3545';
                default: return '#6c757d';
            }
        }

        // وظائف لوحة القيادة الجديدة
        function initializeDashboard() {
            console.log('تهيئة لوحة القيادة...');
            updateDashboardStats();
        }

        function updateDashboardStats() {
            console.log('تحديث إحصائيات لوحة القيادة...');

            // إحصائيات عامة
            updateGeneralStats();

            // إحصائيات التلقيحات
            updateVaccinationStats();

            // إحصائيات المخزون
            updateInventoryStats();
        }

        function updateGeneralStats() {
            // عدد الأطفال المسجلين
            const totalChildren = childrenDatabase ? childrenDatabase.length : 0;
            document.getElementById('totalChildrenStat').textContent = totalChildren;

            // عدد أنواع اللقاحات
            const totalVaccines = vaccineList ? vaccineList.length : 0;
            document.getElementById('totalVaccinesStat').textContent = totalVaccines;

            // عدد أنواع الأدوية
            const totalMedicines = medicineList ? medicineList.length : 0;
            document.getElementById('totalMedicinesStat').textContent = totalMedicines;

            // عدد تقنيات تنظيم الأسرة
            const totalContraceptives = contraceptiveList ? contraceptiveList.length : 0;
            document.getElementById('totalContraceptivesStat').textContent = totalContraceptives;
        }

        function updateVaccinationStats() {
            const progressContainer = document.getElementById('vaccinationProgress');
            if (!progressContainer) return;

            // تعريف اللقاحات الأساسية
            const vaccines = [
                { key: 'BCG', name: 'لقاح السل', frenchName: 'BCG (Tuberculose)' },
                { key: 'VPO0', name: 'شلل الأطفال عند الولادة', frenchName: 'VPO0 (Polio oral à la naissance)' },
                { key: 'VPO1', name: 'شلل الأطفال الأول', frenchName: 'VPO1 (Polio oral 1ère dose)' },
                { key: 'VPO2', name: 'شلل الأطفال الثاني', frenchName: 'VPO2 (Polio oral 2ème dose)' },
                { key: 'VPO3', name: 'شلل الأطفال الثالث', frenchName: 'VPO3 (Polio oral 3ème dose)' },
                { key: 'PENTA1', name: 'اللقاح الخماسي الأول', frenchName: 'Penta1 (Pentavalent 1ère dose)' },
                { key: 'PENTA2', name: 'اللقاح الخماسي الثاني', frenchName: 'Penta2 (Pentavalent 2ème dose)' },
                { key: 'PENTA3', name: 'اللقاح الخماسي الثالث', frenchName: 'Penta3 (Pentavalent 3ème dose)' },
                { key: 'RR1', name: 'الحصبة والحصبة الألمانية الأول', frenchName: 'RR1 (Rougeole-Rubéole 1ère dose)' },
                { key: 'RR2', name: 'الحصبة والحصبة الألمانية الثاني', frenchName: 'RR2 (Rougeole-Rubéole 2ème dose)' }
            ];

            progressContainer.innerHTML = '';

            vaccines.forEach(vaccine => {
                const stats = calculateVaccineStats(vaccine.key);
                const progressItem = createVaccineProgressItem(vaccine, stats);
                progressContainer.appendChild(progressItem);
            });
        }

        function calculateVaccineStats(vaccineKey) {
            if (!childrenDatabase || childrenDatabase.length === 0) {
                return { total: 0, completed: 0, percentage: 0 };
            }

            const total = childrenDatabase.length;
            const completed = childrenDatabase.filter(child =>
                child.completedVaccinations && child.completedVaccinations[vaccineKey]
            ).length;
            const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

            return { total, completed, percentage };
        }

        function createVaccineProgressItem(vaccine, stats) {
            const item = document.createElement('div');
            item.className = 'progress-item';

            const color = getProgressColor(stats.percentage);

            item.innerHTML = `
                <div class="progress-header">
                    <div>
                        <div class="progress-name">${vaccine.name}</div>
                        <div style="font-size: 0.85em; color: #6c757d; font-style: italic;">${vaccine.frenchName}</div>
                    </div>
                    <div class="progress-percentage" style="background: ${color};">
                        ${stats.percentage}%
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${stats.percentage}%; background: ${color};"></div>
                </div>
                <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                    ${stats.completed} من ${stats.total} طفل
                </div>
            `;

            return item;
        }

        function getProgressColor(percentage) {
            if (percentage >= 95) return '#28a745'; // أخضر - ممتاز
            if (percentage >= 80) return '#17a2b8'; // أزرق - جيد
            if (percentage >= 60) return '#ffc107'; // أصفر - متوسط
            return '#dc3545'; // أحمر - ضعيف
        }

        function updateInventoryStats() {
            updateVaccineInventoryStats();
            updateMedicineInventoryStats();
            updateFamilyPlanningInventoryStats();
        }

        function updateVaccineInventoryStats() {
            const container = document.getElementById('vaccineInventoryStats');
            if (!container) return;

            container.innerHTML = '';

            if (!vaccineList || vaccineList.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد لقاحات مضافة بعد</div>';
                return;
            }

            vaccineList.forEach(vaccine => {
                const totalStock = calculateVaccineTotalStock(vaccine.id);
                const item = createInventoryItem(vaccine.name, totalStock, 'جرعة');
                container.appendChild(item);
            });
        }

        function updateMedicineInventoryStats() {
            const container = document.getElementById('medicineInventoryStats');
            if (!container) return;

            container.innerHTML = '';

            if (!medicineList || medicineList.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد أدوية مضافة بعد</div>';
                return;
            }

            medicineList.forEach(medicine => {
                const totalStock = calculateMedicineTotalStock(medicine.id);
                const item = createInventoryItem(medicine.name, totalStock, medicine.unit);
                container.appendChild(item);
            });
        }

        function updateFamilyPlanningInventoryStats() {
            const container = document.getElementById('familyPlanningInventoryStats');
            if (!container) return;

            container.innerHTML = '';

            if (!contraceptiveList || contraceptiveList.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">لا توجد تقنيات تنظيم أسرة مضافة بعد</div>';
                return;
            }

            contraceptiveList.forEach(contraceptive => {
                const totalStock = calculateContraceptiveTotalStock(contraceptive.id);
                const item = createInventoryItem(contraceptive.name, totalStock, 'عدد');
                container.appendChild(item);
            });
        }

        function calculateVaccineTotalStock(vaccineId) {
            if (!monthlyPlanning) return 0;

            let total = 0;
            ['month1', 'month2', 'month3'].forEach(month => {
                if (monthlyPlanning[month] && monthlyPlanning[month].vaccines && monthlyPlanning[month].vaccines[vaccineId]) {
                    total += parseInt(monthlyPlanning[month].vaccines[vaccineId]) || 0;
                }
            });
            return total;
        }

        function calculateMedicineTotalStock(medicineId) {
            if (!medicineMonthlyPlanning) return 0;

            let total = 0;
            ['month1', 'month2', 'month3'].forEach(month => {
                if (medicineMonthlyPlanning[month] && medicineMonthlyPlanning[month].medicines && medicineMonthlyPlanning[month].medicines[medicineId]) {
                    total += parseInt(medicineMonthlyPlanning[month].medicines[medicineId]) || 0;
                }
            });
            return total;
        }

        function calculateContraceptiveTotalStock(contraceptiveId) {
            if (!familyPlanningMonthlyPlanning) return 0;

            let total = 0;
            ['month1', 'month2', 'month3'].forEach(month => {
                if (familyPlanningMonthlyPlanning[month] && familyPlanningMonthlyPlanning[month].contraceptives && familyPlanningMonthlyPlanning[month].contraceptives[contraceptiveId]) {
                    total += parseInt(familyPlanningMonthlyPlanning[month].contraceptives[contraceptiveId]) || 0;
                }
            });
            return total;
        }

        function createInventoryItem(name, quantity, unit) {
            const item = document.createElement('div');
            item.className = 'inventory-item';

            const color = getInventoryColor(quantity);

            item.innerHTML = `
                <div class="inventory-name">${name}</div>
                <div class="inventory-quantity" style="background: ${color};">
                    ${quantity} ${unit}
                </div>
            `;

            return item;
        }

        function getInventoryColor(quantity) {
            if (quantity >= 100) return '#28a745'; // أخضر - مخزون جيد
            if (quantity >= 50) return '#ffc107';  // أصفر - مخزون متوسط
            if (quantity > 0) return '#fd7e14';    // برتقالي - مخزون منخفض
            return '#dc3545';                      // أحمر - نفد المخزون
        }

        function refreshDashboard() {
            console.log('تحديث لوحة القيادة...');
            updateDashboardStats();
            addNotification('تم تحديث إحصائيات لوحة القيادة', 'success');
        }

        function exportDashboardReport() {
            console.log('تصدير تقرير لوحة القيادة...');
            addNotification('ميزة تصدير التقرير قيد التطوير', 'info');
        }

        // تحديث شبكة الأطفال في صفحة السجل
        function updateChildrenGrid() {
            const grid = document.getElementById('childrenGrid');

            if (childrenDatabase.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">لا توجد أطفال مسجلين بعد</div>';
                return;
            }

            grid.innerHTML = '';
            childrenDatabase.forEach(child => {
                const childCard = createChildCard(child);
                grid.appendChild(childCard);
            });
        }

        // إنشاء بطاقة طفل للشبكة
        function createChildCard(child) {
            const card = document.createElement('div');
            card.className = 'child-card';
            card.style.cssText = `
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            `;

            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
                card.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });

            const completedCount = child.vaccinations ? child.vaccinations.filter(v => v.completed).length : 0;
            const totalCount = child.vaccinations ? child.vaccinations.length : 0;
            const completionPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

            card.innerHTML = `
                <h3 style="color: #17a2b8; margin-bottom: 10px;">${child.name}</h3>
                <p style="color: #666; margin-bottom: 15px;">📅 ${child.birthDate}</p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>التلقيحات المكتملة:</span>
                        <span style="font-weight: bold;">${completedCount}/${totalCount}</span>
                    </div>
                    <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                        <div style="background: ${completionPercentage === 100 ? '#28a745' : '#17a2b8'}; height: 100%; width: ${completionPercentage}%; transition: width 0.3s ease;"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 0.9rem; color: #666;">${completionPercentage}%</div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button onclick="loadChildData('${child.id}', event)" style="flex: 1; background: #17a2b8; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📋 عرض</button>
                    <button onclick="deleteChild('${child.id}', event)" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">🗑️</button>
                </div>
            `;

            return card;
        }

        // البحث في سجل الأطفال
        function setupSearch() {
            const searchBox = document.getElementById('searchBox');
            if (searchBox) {
                searchBox.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const grid = document.getElementById('childrenGrid');
                    const cards = grid.querySelectorAll('.child-card');

                    cards.forEach(card => {
                        const name = card.querySelector('h3').textContent.toLowerCase();
                        const birthDate = card.querySelector('p').textContent.toLowerCase();

                        if (name.includes(searchTerm) || birthDate.includes(searchTerm)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }
        }

        // وظائف قاعدة البيانات
        function loadDatabase() {
            if (!currentUser) return;

            const saved = localStorage.getItem(`childrenVaccinationDB_${currentUser.id}`);
            if (saved) {
                childrenDatabase = JSON.parse(saved);
                displayChildrenList();
            }
        }

        function loadUserDatabase() {
            const saved = localStorage.getItem(`childrenVaccinationDB_${currentUser.id}`);
            if (saved) {
                childrenDatabase = JSON.parse(saved);
                displayChildrenList();
            } else {
                childrenDatabase = [];
                displayChildrenList();
            }
        }

        function saveDatabase() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً');
                return;
            }
            localStorage.setItem(`childrenVaccinationDB_${currentUser.id}`, JSON.stringify(childrenDatabase));
        }

        function saveChildToDatabase(name, birthDate, vaccinationDates) {
            const childId = Date.now().toString();
            const existingChildIndex = childrenDatabase.findIndex(child =>
                child.name === name && child.birthDate === birthDate
            );

            const childData = {
                id: childId,
                name: name,
                birthDate: birthDate,
                vaccinationDates: vaccinationDates,
                completedVaccinations: {},
                createdAt: new Date().toISOString()
            };

            if (existingChildIndex !== -1) {
                // تحديث الطفل الموجود
                childData.id = childrenDatabase[existingChildIndex].id;
                childData.completedVaccinations = childrenDatabase[existingChildIndex].completedVaccinations || {};
                childrenDatabase[existingChildIndex] = childData;
                saveDatabase();
                return childData.id;
            } else {
                // إضافة طفل جديد
                childrenDatabase.push(childData);
                saveDatabase();
                return childId;
            }
        }

        function displayChildrenList() {
            if (childrenDatabase.length === 0) {
                childrenList.innerHTML = `
                    <div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">
                        لا توجد سجلات بعد. ابدأ بحساب مواعيد التلقيح لطفل جديد.
                    </div>
                `;
                clearDatabaseBtn.style.display = 'none';
                return;
            }

            clearDatabaseBtn.style.display = 'block';
            childrenList.innerHTML = '';

            // عرض آخر 4 أطفال فقط
            const recentChildren = childrenDatabase.slice(-4).reverse();

            recentChildren.forEach(child => {
                const completedCount = Object.keys(child.completedVaccinations || {}).length;
                const totalCount = child.vaccinationDates.length;
                const progressPercent = Math.round((completedCount / totalCount) * 100);

                const childCard = document.createElement('div');
                childCard.className = 'child-card';
                childCard.innerHTML = `
                    <div class="child-name">${child.name}</div>
                    <div class="child-birth-date">📅 ${child.birthDate}</div>
                    <div class="child-progress">
                        📊 التقدم: ${completedCount}/${totalCount} (${progressPercent}%)
                    </div>
                    <button class="delete-child-btn" onclick="deleteChild('${child.id}', event)">حذف</button>
                `;

                childCard.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('delete-child-btn')) {
                        loadChildData(child.id, e);
                    }
                });

                childrenList.appendChild(childCard);
            });

            // إضافة رسالة إذا كان هناك أطفال أكثر من 4
            if (childrenDatabase.length > 4) {
                const moreInfo = document.createElement('div');
                moreInfo.style.cssText = 'text-align: center; color: #666; margin-top: 15px; font-style: italic;';
                moreInfo.innerHTML = `وهناك ${childrenDatabase.length - 4} طفل/أطفال أخرى في السجل الكامل`;
                childrenList.appendChild(moreInfo);
            }
        }

        function loadChildData(childId, event) {
            console.log('تحميل بيانات الطفل:', childId);

            const child = childrenDatabase.find(c => c.id === childId);
            if (!child) {
                console.error('لم يتم العثور على الطفل:', childId);
                return;
            }

            console.log('تم العثور على الطفل:', child.name);

            // تحديث الحقول
            childNameInput.value = child.name;
            birthDateInput.value = child.birthDate;

            // تحديث المتغيرات الحالية
            currentChildId = childId;
            currentVaccinationDates = child.vaccinationDates;
            currentBirthDate = parseDate(child.birthDate);

            // عرض النتائج مع حالة التلقيحات
            displayResults(child.vaccinationDates, child.name, child.birthDate, child.completedVaccinations);
            marketInput.style.display = 'flex';
            pdfDownloadSection.style.display = 'block';

            // تمييز الطفل المحدد إذا كان event متوفر
            if (event) {
                document.querySelectorAll('.child-card').forEach(card => card.classList.remove('selected'));
                const targetCard = event.target.closest('.child-card');
                if (targetCard) {
                    targetCard.classList.add('selected');
                }
            }

            // التمرير إلى النتائج
            resultsSection.scrollIntoView({ behavior: 'smooth' });

            // إغلاق صفحة سجل الأطفال والعودة للصفحة الرئيسية
            showMainPage();

            console.log('تم تحميل بيانات الطفل بنجاح');
        }

        function deleteChild(childId, event) {
            console.log('محاولة حذف الطفل:', childId);

            // منع انتشار الحدث لتجنب تفعيل click على الكارت
            if (event) {
                event.stopPropagation();
            }

            const child = childrenDatabase.find(c => c.id === childId);
            if (!child) {
                console.error('لم يتم العثور على الطفل:', childId);
                return;
            }

            if (confirm(`هل أنت متأكد من حذف سجل الطفل "${child.name}"؟\n\nسيتم حذف جميع بيانات التلقيحات المرتبطة بهذا الطفل.`)) {
                console.log('تأكيد حذف الطفل:', child.name);

                childrenDatabase = childrenDatabase.filter(c => c.id !== childId);
                saveDatabase();
                displayChildrenList();

                // إذا كان الطفل المحذوف هو المعروض حالياً، امسح النتائج
                if (currentChildId === childId) {
                    childNameInput.value = '';
                    birthDateInput.value = '';
                    resultsSection.style.display = 'none';
                    marketInput.style.display = 'none';
                    pdfDownloadSection.style.display = 'none';
                    currentChildId = null;
                    currentVaccinationDates = null;
                    currentBirthDate = null;
                }

                addNotification(`تم حذف سجل الطفل "${child.name}" بنجاح`, 'info');
                console.log('تم حذف الطفل بنجاح');
            } else {
                console.log('تم إلغاء حذف الطفل');
            }
        }

        function clearDatabase() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً');
                return;
            }

            if (confirm('هل أنت متأكد من حذف جميع السجلات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                childrenDatabase = [];
                localStorage.removeItem(`childrenVaccinationDB_${currentUser.id}`);
                displayChildrenList();

                // مسح النتائج الحالية
                childNameInput.value = '';
                birthDateInput.value = '';
                resultsSection.style.display = 'none';
                currentChildId = null;
            }
        }

        function toggleVaccination(vaccinationIndex) {
            if (!currentChildId) return;

            const child = childrenDatabase.find(c => c.id === currentChildId);
            if (!child) return;

            if (!child.completedVaccinations) {
                child.completedVaccinations = {};
            }

            // تبديل حالة التلقيح
            if (child.completedVaccinations[vaccinationIndex]) {
                delete child.completedVaccinations[vaccinationIndex];
            } else {
                child.completedVaccinations[vaccinationIndex] = {
                    completedAt: new Date().toISOString(),
                    completedDate: new Date().toLocaleDateString('ar-MA')
                };

                // استخدام اللقاح من المخزون
                const vaccinationData = child.vaccinationDates[vaccinationIndex];
                if (vaccinationData && vaccinationData.vaccine) {
                    useVaccine(vaccinationData.vaccine, child.name);
                }
            }

            saveDatabase();
            displayChildrenList();

            // تحديث عرض النتائج
            displayResults(child.vaccinationDates, child.name, child.birthDate, child.completedVaccinations);
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';

                // فحص المستخدم الحالي أولاً
                checkCurrentUser();

                // تهيئة الصفحات
                initializeDashboard();

                // تأخير إضافي للصفحات التي تحتاج currentUser
                setTimeout(() => {
                    if (currentUser) {
                        loadVaccineList();
                    }
                    initializeVaccineManagement();
                    initializeMedicineManagement();
                    initializeFamilyPlanningManagement();
                }, 1000);
            }, 100);
        });
    </script>
</body>
</html>
