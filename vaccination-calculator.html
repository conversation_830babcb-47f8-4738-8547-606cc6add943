<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة مواعيد التلقيح للأطفال</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f9f9f9;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            direction: rtl;
        }

        .container {
            width: calc(100% - 72px);
            margin: 0;
            margin-right: 72px;
            background: white;
            min-height: 100vh;
            transition: all 0.3s ease;
            padding: 0;
            box-sizing: border-box;
        }

        .container.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        

        header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            margin: 0;
            width: 100%;
            box-sizing: border-box;
        }

        header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .input-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }

        .input-row {
            display: flex;
            gap: 30px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: center;
        }

        .input-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        label {
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        input[type="text"] {
            padding: 15px;
            font-size: 1.1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            width: 250px;
            text-align: center;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.3);
        }

        button {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        .error-message {
            color: #e74c3c;
            font-weight: bold;
            text-align: center;
            margin-top: 10px;
            display: none;
        }

        .results-section {
            padding: 30px;
            display: none;
        }

        .results-section h2 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.8rem;
        }

        .pdf-download-section {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 10px;
            border: 2px solid #2196f3;
        }

        .pdf-download-btn {
            background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            margin: 10px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .pdf-download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }

        .pdf-download-btn:active {
            transform: translateY(0);
        }

        .pdf-download-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* أنماط قاعدة البيانات */
        .database-section {
            background: #f8f9fa;
            border-top: 3px solid #17a2b8;
            padding: 30px;
            margin-top: 20px;
        }

        .database-section.mini {
            max-height: 400px;
            overflow: hidden;
        }

        .view-all-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .view-all-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
        }

        /* صفحة سجل الأطفال */
        .children-registry-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .children-registry-page.active {
            display: block;
        }

        .children-registry-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .page-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 20px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        .search-box:focus {
            outline: none;
            border-color: #17a2b8;
            box-shadow: 0 0 10px rgba(23, 162, 184, 0.2);
        }

        .children-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        /* صفحة لوحة القيادة */
        .dashboard-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .dashboard-page.active {
            display: block;
        }

        .dashboard-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-weight: 500;
            color: #495057;
            font-size: 0.9rem;
        }

        .filter-group input, .filter-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 5px;
            font-size: 0.9rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .stat-card h3 {
            color: #6f42c1;
            margin-bottom: 15px;
            font-size: 1.1rem;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 10px;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }

        .stat-description {
            color: #6c757d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .detailed-stats {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .detailed-stats h2 {
            color: #6f42c1;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .stats-table th,
        .stats-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .stats-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .stats-table tr:hover {
            background: #f8f9fa;
        }

        .vaccine-name-container {
            line-height: 1.3;
        }

        .vaccine-name-container .arabic-text {
            text-align: right;
            direction: rtl;
        }

        .vaccine-name-container .french-text {
            text-align: left;
            direction: ltr;
        }

        /* صفحة تدبير اللقاحات */
        .vaccine-management-page {
            display: none;
            padding: 20px;
            background: white;
            min-height: 100vh;
            width: calc(100% - 72px);
            margin-right: 72px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .vaccine-management-page.active {
            display: block;
        }

        .vaccine-management-page.sidebar-open {
            width: calc(100% - 280px);
            margin-right: 280px;
        }

        .management-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stock-management-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .stock-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stock-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .stock-item h4 {
            color: #28a745;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .stock-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .stock-input {
            width: 80px;
            padding: 5px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            text-align: center;
        }

        .stock-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
        }

        .stock-status {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .stock-status.good {
            background: #d4edda;
            color: #155724;
        }

        .stock-status.warning {
            background: #fff3cd;
            color: #856404;
        }

        .stock-status.critical {
            background: #f8d7da;
            color: #721c24;
        }

        .notifications-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .notification-item {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
        }

        .notification-item.warning {
            background: #fff8e1;
            border-left-color: #ff9800;
        }

        .notification-item.success {
            background: #e8f5e8;
            border-left-color: #4caf50;
        }

        .prediction-section {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .prediction-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .prediction-table th,
        .prediction-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #e9ecef;
        }

        .prediction-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .shortage-highlight {
            background: #ffebee !important;
            color: #c62828;
            font-weight: bold;
        }

        /* أنماط الشريط الجانبي للصفحات */
        .sidebar-menu {
            padding: 16px 0;
            border-bottom: 1px solid #333;
        }

        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: #fff;
            text-decoration: none;
            transition: background-color 0.2s ease;
            cursor: pointer;
        }

        .sidebar-menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu-item.active {
            background: #065fd4;
        }

        .sidebar-menu-icon {
            margin-left: 12px;
            font-size: 1.1rem;
        }

        .database-section h2 {
            color: #17a2b8;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .children-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .child-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .child-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            border-color: #17a2b8;
        }

        .child-card.selected {
            border-color: #17a2b8;
            background: #e7f3ff;
        }

        .child-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 8px;
        }

        .child-birth-date {
            color: #666;
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .child-progress {
            font-size: 0.9rem;
            color: #28a745;
        }

        .delete-child-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            float: left;
            margin-top: 10px;
        }

        .delete-child-btn:hover {
            background: #c82333;
        }

        .vaccination-checkbox {
            margin-left: 10px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .vaccination-item.completed {
            background: #d4edda;
            border-color: #c3e6cb;
        }

        .vaccination-item.completed .vaccination-age {
            color: #155724;
        }

        .vaccination-item.completed .vaccination-date {
            background: #d1ecf1;
            color: #0c5460;
        }

        .clear-database-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 15px;
            float: left;
        }

        .clear-database-btn:hover {
            background: #c82333;
        }

        /* الشريط الجانبي مثل YouTube */
        .sidebar {
            position: fixed;
            top: 0;
            right: -280px;
            width: 280px;
            height: 100vh;
            background: #212121;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
            transition: right 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
            border-left: 1px solid #333;
        }

        .sidebar.open {
            right: 0;
        }

        .sidebar-header {
            background: #181818;
            padding: 16px 24px;
            border-bottom: 1px solid #333;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo {
            width: 32px;
            height: 32px;
            background: #ff0000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .sidebar-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            margin: 0;
        }

        .sidebar-subtitle {
            color: #aaa;
            font-size: 0.8rem;
            margin: 0;
        }

        .sidebar-content {
            padding: 0;
        }

        .auth-form {
            background: #181818;
            border-radius: 0;
            padding: 24px;
            margin-bottom: 0;
            border-bottom: 1px solid #333;
        }

        .form-title {
            color: white;
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: right;
        }

        .form-group-sidebar {
            margin-bottom: 12px;
        }

        .form-group-sidebar label {
            color: #fff;
            font-size: 0.85rem;
            margin-bottom: 6px;
            display: block;
            font-weight: 400;
        }

        .form-group-sidebar input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #333;
            border-radius: 2px;
            background: #0f0f0f;
            color: white;
            font-size: 0.9rem;
            transition: border-color 0.2s ease;
        }

        .form-group-sidebar input::placeholder {
            color: #717171;
        }

        .form-group-sidebar input:focus {
            outline: none;
            border-color: #065fd4;
            background: #0f0f0f;
        }

        .sidebar-btn {
            width: 100%;
            padding: 10px 16px;
            background: #065fd4;
            color: white;
            border: none;
            border-radius: 2px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-bottom: 8px;
        }

        .sidebar-btn:hover {
            background: #0856c7;
        }

        .sidebar-btn.secondary {
            background: #606060;
        }

        .sidebar-btn.secondary:hover {
            background: #4a4a4a;
        }

        .sidebar-btn.danger {
            background: #cc0000;
        }

        .sidebar-btn.danger:hover {
            background: #aa0000;
        }

        .toggle-btn {
            background: transparent;
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: auto;
        }

        .toggle-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }



        /* شريط جانبي مصغر */
        .mini-sidebar {
            position: fixed;
            top: 0;
            right: 0;
            width: 72px;
            height: 100vh;
            background: #212121;
            border-left: 1px solid #333;
            z-index: 998;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 8px;
        }

        .mini-sidebar.hidden {
            right: -72px;
        }

        .mini-sidebar-toggle {
            width: 56px;
            height: 40px;
            background: transparent;
            border: none;
            color: #fff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-radius: 4px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mini-sidebar-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .mini-sidebar-icon {
            width: 40px;
            height: 40px;
            background: #ff0000;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 16px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .mini-sidebar-icon:hover {
            background: #cc0000;
        }

        .mini-sidebar-pages {
            margin-top: 20px;
        }

        .mini-page-icon {
            width: 40px;
            height: 40px;
            background: transparent;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #aaa;
            font-size: 1.1rem;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .mini-page-icon:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .mini-page-icon.active {
            background: #065fd4;
            color: #fff;
        }

        .mini-page-icon.active::after {
            content: '';
            position: absolute;
            right: -8px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: #065fd4;
            border-radius: 2px;
        }

        /* تعديل الشريط الجانبي الكامل */
        .sidebar.open ~ .mini-sidebar {
            right: -72px;
        }

        .user-info {
            background: #181818;
            border-bottom: 1px solid #333;
            padding: 16px 24px;
            margin-bottom: 0;
        }

        .user-info h3 {
            color: #fff;
            margin-bottom: 8px;
            font-size: 1rem;
            font-weight: 500;
        }

        .user-info p {
            color: #aaa;
            margin: 4px 0;
            font-size: 0.85rem;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .auth-toggle {
            text-align: center;
            margin-top: 15px;
        }

        .auth-toggle a {
            color: #3498db;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .auth-toggle a:hover {
            text-decoration: underline;
        }

       


        .vaccination-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 15px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .vaccination-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .vaccination-age {
            font-size: 1.3rem;
            font-weight: bold;
            color: #4CAF50;
            margin-bottom: 10px;
        }

        .vaccination-vaccines {
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 10px;
            line-height: 1.5;
        }

        .vaccination-date {
            font-size: 1.2rem;
            font-weight: bold;
            color: #e74c3c;
            background: #fff5f5;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }

        .vaccine-item {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .vaccine-item:hover {
            background: #e9ecef;
        }

       .vaccine-name {
    font-size: 1rem;
    color: #495057;
    font-weight: 500;
    flex: 1;
    text-align: left !important;
    direction: ltr !important;
    background: #e7eaf1;
    padding: 10px;
    border-radius: 28px;
    margin-left: 5px;
}

        .vaccine-location {
            font-size: 0.9rem;
            font-weight: bold;
            color: #fff;
            background: #2196f3;
            padding: 4px 12px;
            border-radius: 15px;
            margin-left: 30px;
            text-align: right;
            direction: rtl;
        }

        .vaccine-location.mouth {
            background: #4caf50;
        }

        .vaccine-location.left-arm {
            background: #ff9800;
        }

        .vaccine-location.right-arm {
            background: #e91e63;
        }

        .vaccine-location.left-thigh {
            background: #9c27b0;
        }

        .vaccine-location.right-thigh {
            background: #3f51b5;
        }

        .market-input {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 10px;
            display: none;
        }

        .market-input label {
            font-size: 1.1rem;
            font-weight: bold;
            color: #e65100;
            text-align: center;
        }

        select {
            padding: 12px 20px;
            font-size: 1.1rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #333;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        select:focus {
            outline: none;
            border-color: #ff9800;
            box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
        }

        .market-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .market-month {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .haha{
            color: #c5c5c5;
            font-size: 12px;
            margin-left: 5px;
        }
        .market-month h3 {
            color: #ff9800;
            margin-bottom: 15px;
            font-size: 1.3rem;
            text-align: center;
            border-bottom: 2px solid #ff9800;
            padding-bottom: 10px;
        }

        .market-date {
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 5px 0;
            text-align: center;
            font-weight: bold;
            color: #e65100;
        }

        .market-date.vaccination-day {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        .market-date.vaccination-day::after {
            content: " 💉";
        }

        .market-dates-section {
            margin-top: 15px;
            padding: 12px;
            background: #fff3e0;
            border: 1px solid #ffcc02;
            border-radius: 8px;
            display: none;
        }

        .market-dates-section.show {
            display: block;
        }

        .market-dates-title {
            font-size: 1rem;
            font-weight: bold;
            color: #e65100;
            margin-bottom: 8px;
            text-align: center;
        }

        .market-dates-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .market-date-item {
            background: #ff9800;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .market-date-item.exact-match {
            background: #4caf50;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* أنماط خاصة للنصوص العربية والفرنسية */
        .arabic-text {
            text-align: right;
            direction: rtl;
        }

        .french-text {
            text-align: left;
            direction: ltr;
           
        }

        .vaccination-age {
            text-align: left;
            direction: ltr;
        }

        .vaccination-date {
            text-align: center;
            direction: ltr;
        }

        /* معلومات المطور */
        .developer-info {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            margin-top: 30px;
            border-top: 3px solid #3498db;
        }

        .developer-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
        }

        .developer-avatar {
    font-size: 3rem;
    background: linear-gradient(135deg, #46a24a, #7452ac);
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}
        .developer-details {
            text-align: left;
        }

        .developer-name {
    font-size: 1.4rem;
    font-weight: bold;
    color: #ffffff;
    text-transform: uppercase;
    margin-bottom: 5px;
}

        .developer-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 3px;
        }

        .developer-subtitle {
            font-size: 0.9rem;
            color: #bdc3c7;
            font-style: italic;
        }

        .developer-credit {
            font-size: 0.9rem;
            color: #95a5a6;
            border-top: 1px solid #34495e;
            padding-top: 15px;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            header h1 {
                font-size: 2rem;
            }
            
            .input-section, .results-section {
                padding: 20px;
            }
            
            input[type="text"] {
                width: 100%;
                max-width: 300px;
            }
            
            .form-group {
                gap: 10px;
            }

            .input-row {
                flex-direction: column;
                gap: 15px;
            }
            
            .vaccination-item {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            header h1 {
                font-size: 1.5rem;
            }

            header p {
                font-size: 1rem;
            }

            .vaccination-age {
                font-size: 1.1rem;
            }

            .vaccination-vaccines {
                font-size: 1rem;
            }

            .vaccination-date {
                font-size: 1.1rem;
            }

            .vaccine-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .vaccine-name {
                font-size: 0.9rem;
            }

            .vaccine-location {
                font-size: 0.8rem;
                align-self: flex-end;
            }

            .market-input {
                padding: 15px;
                gap: 10px;
            }

            .market-dates-list {
                gap: 5px;
            }

            .market-date-item {
                font-size: 0.8rem;
                padding: 3px 6px;
            }

            select {
                width: 100%;
                max-width: 300px;
            }

            .developer-info {
                padding: 20px 15px;
            }

            .developer-container {
                flex-direction: column;
                gap: 15px;
            }

            .developer-avatar {
                width: 60px;
                height: 60px;
                font-size: 2rem;
            }

            .developer-details {
                text-align: center;
            }

            .developer-name {
                font-size: 1.2rem;
            }

            .developer-title {
                font-size: 1rem;
            }

            .developer-subtitle {
                font-size: 0.8rem;
            }

            .developer-credit {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>


    <!-- الطبقة الشفافة -->
    <div class="overlay" id="overlay"></div>

    <!-- الشريط الجانبي المصغر -->
    <div class="mini-sidebar" id="miniSidebar">
        <button class="mini-sidebar-toggle" id="toggleSidebar">☰</button>
        <div class="mini-sidebar-icon" onclick="toggleSidebar()">⚕️</div>

        <!-- أيقونات الصفحات -->
        <div class="mini-sidebar-pages" id="miniSidebarPages" style="display: none;">
            <div class="mini-page-icon active" onclick="showMainPage()" title="الصفحة الرئيسية">
                🏠
            </div>
            <div class="mini-page-icon" onclick="showDashboard()" title="لوحة القيادة">
                📊
            </div>
            <div class="mini-page-icon" onclick="showVaccineManagement()" title="تدبير اللقاحات">
                💉
            </div>
            <div class="mini-page-icon" onclick="showChildrenRegistry()" title="سجل الأطفال">
                👶
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">⚕️</div>
            <div>
                <div class="sidebar-title">نظام إدارة التلقيح</div>
                <div class="sidebar-subtitle">تسجيل الدخول للممرضين</div>
            </div>
            <button class="toggle-btn" id="sidebarToggleBtn">✕</button>
        </div>

        <div class="sidebar-content">
            <!-- معلومات المستخدم المسجل -->
            <div class="user-info" id="userInfo" style="display: none;">
                <h3>مرحباً بك</h3>
                <p><strong>الممرض/ة:</strong> <span id="currentUserName"></span></p>
                <p><strong>المركز:</strong> <span id="currentCenter"></span></p>
                <p><strong>الإقليم:</strong> <span id="currentRegion"></span></p>
                <button class="sidebar-btn danger" onclick="logout()">تسجيل الخروج</button>
            </div>

            <!-- نموذج تسجيل الدخول -->
            <div class="auth-form" id="loginForm">
                <div class="form-title">تسجيل الدخول</div>

                <div class="form-group-sidebar">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="loginUsername" placeholder="أدخل اسم المستخدم">
                </div>

                <div class="form-group-sidebar">
                    <label>كلمة المرور:</label>
                    <input type="password" id="loginPassword" placeholder="أدخل كلمة المرور">
                </div>

                <button class="sidebar-btn" onclick="login()">تسجيل الدخول</button>

                <div class="auth-toggle">
                    <a href="#" onclick="showRegisterForm()">إنشاء حساب جديد</a>
                </div>
            </div>

            <!-- نموذج التسجيل -->
            <div class="auth-form" id="registerForm" style="display: none;">
                <div class="form-title">إنشاء حساب جديد</div>

                <div class="form-group-sidebar">
                    <label>اسم الممرض/ة:</label>
                    <input type="text" id="registerName" placeholder="الاسم الكامل">
                </div>

                <div class="form-group-sidebar">
                    <label>اسم المستخدم:</label>
                    <input type="text" id="registerUsername" placeholder="اختر اسم مستخدم">
                </div>

                <div class="form-group-sidebar">
                    <label>كلمة المرور:</label>
                    <input type="password" id="registerPassword" placeholder="اختر كلمة مرور قوية">
                </div>

                <div class="form-group-sidebar">
                    <label>اسم المركز الصحي:</label>
                    <input type="text" id="registerCenter" placeholder="مثال: المركز الصحي الحضري">
                </div>

                <div class="form-group-sidebar">
                    <label>الإقليم:</label>
                    <input type="text" id="registerRegion" placeholder="مثال: الدار البيضاء">
                </div>

                <button class="sidebar-btn" onclick="register()">إنشاء الحساب</button>

                <div class="auth-toggle">
                    <a href="#" onclick="showLoginForm()">لديك حساب؟ سجل الدخول</a>
                </div>
            </div>

            <!-- قائمة الصفحات -->
            <div class="sidebar-menu" id="sidebarMenu" style="display: none;">
                <div class="sidebar-menu-item active" onclick="showMainPage()">
                    <span class="sidebar-menu-icon">🏠</span>
                    <span>الصفحة الرئيسية</span>
                </div>
                <div class="sidebar-menu-item" onclick="showDashboard()">
                    <span class="sidebar-menu-icon">📊</span>
                    <span>لوحة القيادة</span>
                </div>
                <div class="sidebar-menu-item" onclick="showVaccineManagement()">
                    <span class="sidebar-menu-icon">💉</span>
                    <span>تدبير اللقاحات</span>
                </div>
                <div class="sidebar-menu-item" onclick="showChildrenRegistry()">
                    <span class="sidebar-menu-icon">👶</span>
                    <span>سجل الأطفال</span>
                </div>
            </div>
        </div>
        </div>

        <!-- صفحة لوحة القيادة -->
        <div class="dashboard-page" id="dashboardPage">
            <div class="dashboard-header">
                <h1>📊 لوحة القيادة - إحصائيات التلقيح</h1>
                <p>نظرة شاملة على معدلات التغطية التلقيحية في المركز الصحي</p>
            </div>

            <div class="stats-filters">
                <div class="filter-group">
                    <label>من تاريخ:</label>
                    <input type="date" id="startDate" onchange="updateDashboard()">
                </div>
                <div class="filter-group">
                    <label>إلى تاريخ:</label>
                    <input type="date" id="endDate" onchange="updateDashboard()">
                </div>
                <div class="filter-group">
                    <label>فترة العرض:</label>
                    <select id="periodFilter" onchange="updateDashboard()">
                        <option value="all">جميع الفترات</option>
                        <option value="month">الشهر الحالي</option>
                        <option value="quarter">الربع الحالي</option>
                        <option value="year">السنة الحالية</option>
                    </select>
                </div>
                <button onclick="updateDashboard()" class="btn btn-primary">تحديث الإحصائيات</button>
            </div>

            <div class="stats-grid" id="statsGrid">
                <!-- سيتم ملء الإحصائيات هنا -->
            </div>

            <div class="detailed-stats">
                <h2>📈 تفاصيل معدلات التغطية التلقيحية</h2>
                <table class="stats-table" id="detailedStatsTable">
                    <thead>
                        <tr>
                            <th>اللقاح</th>
                            <th>العدد المطلوب</th>
                            <th>العدد المنجز</th>
                            <th>معدل التغطية</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="detailedStatsBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- صفحة تدبير اللقاحات -->
        <div class="vaccine-management-page" id="vaccineManagementPage">
            <div class="management-header">
                <h1>💉 تدبير مخزون اللقاحات</h1>
                <p>إدارة وتتبع مخزون اللقاحات في المركز الصحي</p>
            </div>

            <div class="stock-management-section">
                <h2>📦 المخزون الحالي</h2>
                <div class="stock-grid" id="stockGrid">
                    <!-- سيتم ملء بيانات المخزون هنا -->
                </div>
                <button onclick="updateAllStock()" class="btn btn-primary">تحديث جميع المخزونات</button>
            </div>

            <div class="notifications-section">
                <h2>🔔 إشعارات الاستخدام</h2>
                <div id="notificationsList">
                    <!-- سيتم عرض الإشعارات هنا -->
                </div>
            </div>

            <div class="prediction-section">
                <h2>📈 توقعات المخزون (3 أشهر قادمة)</h2>
                <p>بناءً على معدل الاستهلاك في الأشهر السابقة</p>
                <table class="prediction-table" id="predictionTable">
                    <thead>
                        <tr>
                            <th>اللقاح</th>
                            <th>المخزون الحالي</th>
                            <th>الشهر القادم</th>
                            <th>الشهر الثاني</th>
                            <th>الشهر الثالث</th>
                            <th>حالة التوفر</th>
                        </tr>
                    </thead>
                    <tbody id="predictionTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
                <button onclick="downloadStockReport()" class="btn btn-success" style="margin-top: 15px;">
                    📄 تحميل تقرير المخزون PDF
                </button>
            </div>
        </div>

        <!-- صفحة سجل الأطفال -->
        <div class="children-registry-page" id="childrenRegistryPage">
            <button class="back-btn" onclick="showMainPage()">← العودة للصفحة الرئيسية</button>

            <div class="page-header">
                <h1>📋 سجل الأطفال الكامل</h1>
                <p>جميع الأطفال المسجلين في النظام</p>
            </div>

            <input type="text" class="search-box" id="searchBox" placeholder="🔍 البحث عن طفل (الاسم أو تاريخ الولادة)...">

            <div class="children-grid" id="childrenGrid">
                <!-- سيتم ملء هذا القسم بـ JavaScript -->
            </div>
        </div>
    </div>

    <div class="container">
        <!-- الصفحة الرئيسية -->
        <div class="main-page" id="mainPage">
            <header>
                <h1>حاسبة مواعيد التلقيح للأطفال</h1>
                <p>حسب الجدول الوطني المغربي للتلقيح والتغذية</p>
            </header>

        <div class="input-section">
            <div class="form-group">
                <div class="input-row">
                    <div class="input-field">
                        <label for="childName">اسم الطفل:</label>
                        <input type="text" id="childName" placeholder="أدخل اسم الطفل">
                    </div>
                    <div class="input-field">
                        <label for="birthDate">تاريخ الولادة:</label>
                        <input type="text" id="birthDate" placeholder="DD/MM/YYYY" maxlength="10">
                    </div>
                </div>
                <button id="calculateBtn">احسب مواعيد التلقيح</button>
            </div>
            <div class="error-message" id="errorMessage"></div>
        </div>

        <div class="results-section" id="resultsSection">
            <h2 id="resultsTitle">جدول مواعيد التلقيح</h2>
            <div id="childInfo" style="text-align: center; margin-bottom: 20px; font-size: 1.1rem; color: #333; display: none;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 2px solid #4CAF50;">
                    <strong>اسم الطفل:</strong> <span id="displayChildName"></span> |
                    <strong>تاريخ الولادة:</strong> <span id="displayBirthDate"></span>
                </div>
            </div>

            <div class="market-input" id="marketInput">
                <label for="marketDay">🛒 اختر يوم السوق الأسبوعي لمعرفة التواريخ المناسبة:</label>
                <select id="marketDay">
                    <option value="">-- اختر يوم --</option>
                    <option value="0">الأحد</option>
                    <option value="1">الاثنين</option>
                    <option value="2">الثلاثاء</option>
                    <option value="3">الأربعاء</option>
                    <option value="4">الخميس</option>
                    <option value="5">الجمعة</option>
                    <option value="6">السبت</option>
                </select>
            </div>

            <div class="vaccination-schedule" id="vaccinationSchedule">
            </div>

            <div class="pdf-download-section" id="pdfDownloadSection" style="display: none;">
                <h3 style="color: #1976d2; margin-bottom: 15px;">📄 تحميل النتائج</h3>
                <button class="pdf-download-btn" id="downloadPdfBtn">
                    📥 تحميل جدول التلقيح PDF
                </button>
                <p style="color: #666; font-size: 0.9rem; margin-top: 10px;">
                    سيتم تحميل ملف PDF يحتوي على صورة كاملة لجدول مواعيد التلقيح
                </p>
            </div>
        </div>

      

        <!-- قسم قاعدة البيانات -->
        <div class="database-section mini">
            <h2>📋 سجل الأطفال</h2>
            <p style="text-align: center; color: #666; margin-bottom: 20px;">
                آخر الأطفال المسجلين (آخر 4 أطفال)
            </p>

            <div class="children-list" id="childrenList">
                <div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">
                    لا توجد سجلات بعد. ابدأ بحساب مواعيد التلقيح لطفل جديد.
                </div>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="view-all-btn" onclick="showChildrenRegistry()">📋 عرض جميع الأطفال</button>
                <button class="clear-database-btn" id="clearDatabaseBtn" style="display: none;">
                    🗑️ مسح جميع السجلات
                </button>
            </div>
            <div style="clear: both;"></div>
        </div>

        <!-- قسم المطور -->
        <footer class="developer-info">
            <div class="developer-container">
                <div class="developer-avatar">👨‍⚕️</div>
                <div class="developer-details">
                    <div class="haha"> تمت برمجة هذه الأداة من طرف </div>
                    <div class="developer-name professional-text">Jamal Chafik</div>
                    <div class="developer-title">Infirmier Diplômé d'État</div>
                    <div class="developer-subtitle">Infirmier Polyvalent</div>
                </div>
            </div>
            <div class="developer-credit">
                version 1.0
            </div>
        </footer>
    </div>

    <!-- مكتبة jsPDF لإنشاء ملفات PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <script>
        const vaccinationSchedule = [
            {
                age: "naissance",
                months: 0,
                additionalDays: 0,
                vaccines: [
                    { name: "HB1 (0.5 ml en IM)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "1 mois",
                months: 1,
                additionalDays: 0,
                vaccines: [
                    { name: "BCG (0.05ml en ID)", location: "يد يسرى" },
                    { name: "VPO0 (2 gouttes)", location: "الفم" },
                    { name: "Vit D 100 000 ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "2 mois",
                months: 2,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta1 (DTC+Hib+HB) (0.5 ml en IM)", location: "فخد يمنى" },
                    { name: "VPO1", location: "الفم" },
                    { name: "Rota1 (2 ml)", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "2.5 mois",
                months: 2,
                additionalDays: 15,
                vaccines: [
                    { name: "VPC 1 (Pneumo 1) (0.5 ml en IM)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "3 mois",
                months: 3,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta2 (DTC+Hib+HB)", location: "فخد يمنى" },
                    { name: "VPO2", location: "الفم" },
                    { name: "Rota2", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "4 mois",
                months: 4,
                additionalDays: 0,
                vaccines: [
                    { name: "Penta3 (DTC+Hib+HB)", location: "فخد يمنى" },
                    { name: "VPO3", location: "الفم" },
                    { name: "Rota3", location: "الفم" },
                    { name: "VPI1 (0.5 ml)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "4.5 mois",
                months: 4,
                additionalDays: 15,
                vaccines: [
                    { name: "VPC 2 (Pneumo 2)", location: "فخد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "6 mois",
                months: 6,
                additionalDays: 0,
                vaccines: [
                    { name: "VPC 3 (Pneumo 3)", location: "فخد يسرى" },
                    { name: "Vit D 100 000ui", location: "الفم" },
                    { name: "Vit A 100 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "9 mois",
                months: 9,
                additionalDays: 0,
                vaccines: [
                    { name: "RR1 (rougeole-rubéole combiné) (0.5 ml en s/c)", location: "يد يسرى" },
                    { name: "VPI2 (0.5ml)", location: "فخد يسرى" },
                    { name: "Vit A 200 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "12 mois",
                months: 12,
                additionalDays: 0,
                vaccines: [
                    { name: "VPC 4 (Pneumo 4)", location: "يد يسرى" }
                ],
                additionalInfo: ""
            },
            {
                age: "18 mois",
                months: 18,
                additionalDays: 0,
                vaccines: [
                    { name: "VPO4", location: "الفم" },
                    { name: "DTC rappel 1 (0.5 ml en IM)", location: "يد يسرى" },
                    { name: "RR2", location: "يد يمنى" },
                    { name: "Vit A 200 000ui", location: "الفم" }
                ],
                additionalInfo: ""
            },
            {
                age: "5 ans",
                months: 60,
                additionalDays: 0,
                vaccines: [
                    { name: "VPO5", location: "الفم" },
                    { name: "DTC rappel 2", location: "يد يسرى" }
                ],
                additionalInfo: ""
            }
        ];

        const childNameInput = document.getElementById('childName');
        const birthDateInput = document.getElementById('birthDate');
        const calculateBtn = document.getElementById('calculateBtn');
        const errorMessage = document.getElementById('errorMessage');
        const resultsSection = document.getElementById('resultsSection');
        const vaccinationScheduleDiv = document.getElementById('vaccinationSchedule');
        const marketInput = document.getElementById('marketInput');
        const marketDay = document.getElementById('marketDay');
        const pdfDownloadSection = document.getElementById('pdfDownloadSection');
        const downloadPdfBtn = document.getElementById('downloadPdfBtn');
        const childrenList = document.getElementById('childrenList');
        const clearDatabaseBtn = document.getElementById('clearDatabaseBtn');

        let currentVaccinationDates = [];
        let currentBirthDate = null;
        let currentChildId = null;
        let childrenDatabase = [];
        let currentUser = null;
        let usersDatabase = [];

        calculateBtn.addEventListener('click', calculateVaccinationDates);
        marketDay.addEventListener('change', updateMarketDates);
        downloadPdfBtn.addEventListener('click', generatePDF);
        clearDatabaseBtn.addEventListener('click', clearDatabase);

        // إعداد الشريط الجانبي
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('overlay');
        const toggleBtn = document.getElementById('toggleSidebar');
        const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
        const miniSidebar = document.getElementById('miniSidebar');

        toggleBtn.addEventListener('click', toggleSidebar);
        sidebarToggleBtn.addEventListener('click', closeSidebar);
        overlay.addEventListener('click', closeSidebar);

        // تحميل قاعدة البيانات عند بدء التشغيل
        loadDatabase();
        loadUsers();
        checkCurrentUser();
        initializeSidebar();
        setupSearch();

        birthDateInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                calculateVaccinationDates();
            }
        });

        birthDateInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2);
            }
            if (value.length >= 5) {
                value = value.substring(0, 5) + '/' + value.substring(5, 9);
            }
            e.target.value = value;
        });

        function isValidDate(dateString) {
            const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
            const match = dateString.match(regex);

            if (!match) return false;

            const day = parseInt(match[1], 10);
            const month = parseInt(match[2], 10);
            const year = parseInt(match[3], 10);

            if (month < 1 || month > 12) return false;
            if (day < 1 || day > 31) return false;
            if (year < 1900 || year > new Date().getFullYear()) return false;

            const date = new Date(year, month - 1, day);
            return date.getDate() === day && date.getMonth() === month - 1 && date.getFullYear() === year;
        }

        function parseDate(dateString) {
            const parts = dateString.split('/');
            return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }

        function formatDate(date) {
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}/${month}/${year}`;
        }

        function addMonthsAndDays(date, months, additionalDays) {
            function getDaysInMonth(year, month) {
                return new Date(year, month + 1, 0).getDate();
            }

            let result = new Date(date);
            const originalDay = date.getDate();

            // حساب الأشهر بدقة - عد الأيام الفعلي من اليوم المحدد
            for (let i = 0; i < months; i++) {
                const currentYear = result.getFullYear();
                const currentMonth = result.getMonth();
                const currentDay = result.getDate();
                const daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

                // حساب الأيام المتبقية في الشهر الحالي (من اليوم الحالي إلى نهاية الشهر)
                const daysRemainingInMonth = daysInCurrentMonth - currentDay + 1;

                // إضافة الأيام المتبقية للانتقال لبداية الشهر التالي
                result.setDate(result.getDate() + daysRemainingInMonth);

                // الآن نحن في اليوم الأول من الشهر التالي
                // نحتاج للوصول لنفس اليوم الأصلي في هذا الشهر الجديد
                const newMonth = result.getMonth();
                const newYear = result.getFullYear();
                const daysInNewMonth = getDaysInMonth(newYear, newMonth);

                // تحديد اليوم المستهدف في الشهر الجديد
                let targetDay;
                if (originalDay <= daysInNewMonth) {
                    targetDay = originalDay;
                } else {
                    // إذا كان اليوم الأصلي غير موجود في الشهر الجديد (مثل 31 في فبراير)
                    targetDay = daysInNewMonth;
                }

                // الانتقال لليوم المستهدف (نطرح 1 لأننا في اليوم الأول)
                result.setDate(targetDay);
            }

            // إضافة الأيام الإضافية
            if (additionalDays > 0) {
                result.setDate(result.getDate() + additionalDays);
            }

            return result;
        }

        function calculateVaccinationDates() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً للوصول إلى هذه الميزة');
                toggleSidebar();
                return;
            }

            const childNameValue = childNameInput.value.trim();
            const birthDateValue = birthDateInput.value.trim();

            errorMessage.style.display = 'none';

            if (!childNameValue) {
                showError('يرجى إدخال اسم الطفل');
                return;
            }

            if (!birthDateValue) {
                showError('يرجى إدخال تاريخ الولادة');
                return;
            }

            if (!isValidDate(birthDateValue)) {
                showError('يرجى إدخال تاريخ صحيح بصيغة DD/MM/YYYY');
                return;
            }

            const birthDate = parseDate(birthDateValue);

            if (birthDate > new Date()) {
                showError('تاريخ الولادة لا يمكن أن يكون في المستقبل');
                return;
            }

            const vaccinationDates = vaccinationSchedule.map(vaccination => {
                const vaccinationDate = addMonthsAndDays(birthDate, vaccination.months, vaccination.additionalDays);
                return {
                    ...vaccination,
                    date: vaccinationDate,
                    formattedDate: formatDate(vaccinationDate)
                };
            });

            currentVaccinationDates = vaccinationDates;
            currentBirthDate = birthDate;

            // حفظ الطفل في قاعدة البيانات
            currentChildId = saveChildToDatabase(childNameValue, birthDateValue, vaccinationDates);

            displayResults(vaccinationDates, childNameValue, birthDateValue);
            marketInput.style.display = 'flex';
            pdfDownloadSection.style.display = 'block';

            // تحديث عرض قاعدة البيانات
            displayChildrenList();
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            resultsSection.style.display = 'none';
        }

        function getLocationClass(location) {
            switch(location) {
                case 'الفم': return 'mouth';
                case 'يد يسرى': return 'left-arm';
                case 'يد يمنى': return 'right-arm';
                case 'فخد يسرى': return 'left-thigh';
                case 'فخد يمنى': return 'right-thigh';
                default: return '';
            }
        }

        function displayResults(vaccinationDates, childName, birthDate, completedVaccinations = {}) {
            // عرض معلومات الطفل
            document.getElementById('displayChildName').textContent = childName;
            document.getElementById('displayBirthDate').textContent = birthDate;
            document.getElementById('childInfo').style.display = 'block';

            vaccinationScheduleDiv.innerHTML = '';

            vaccinationDates.forEach((vaccination, index) => {
                const isCompleted = completedVaccinations && completedVaccinations[index];
                const vaccinationItem = document.createElement('div');
                vaccinationItem.className = `vaccination-item ${isCompleted ? 'completed' : ''}`;

                let vaccinesHtml = '';
                vaccination.vaccines.forEach(vaccine => {
                    const locationClass = getLocationClass(vaccine.location);
                    vaccinesHtml += `
                        <div class="vaccine-item">
                            <div class="vaccine-name french-text">${vaccine.name}</div>
                            <div class="vaccine-location ${locationClass} arabic-text">📍 ${vaccine.location}</div>
                        </div>
                    `;
                });

                const checkboxHtml = currentChildId ? `
                    <div style="text-align: center; margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                        <label style="display: flex; align-items: center; justify-content: center; gap: 10px; cursor: pointer;">
                            <input type="checkbox" class="vaccination-checkbox"
                                   ${isCompleted ? 'checked' : ''}
                                   onchange="toggleVaccination(${index})">
                            <span style="font-weight: bold; color: ${isCompleted ? '#28a745' : '#6c757d'};">
                                ${isCompleted ? '✅ تم إنجاز التلقيح' : '⏳ لم يتم إنجاز التلقيح بعد'}
                            </span>
                        </label>

                    </div>
                ` : '';

                vaccinationItem.innerHTML = `
                    <div class="vaccination-age french-text">${vaccination.age}</div>
                    <div class="vaccination-date">${vaccination.formattedDate}</div>
                    ${vaccinesHtml}
                    ${vaccination.additionalInfo ? `<div class="vaccination-vaccines french-text" style="color: #666; font-style: italic; margin-top: 10px;">${vaccination.additionalInfo}</div>` : ''}
                    ${checkboxHtml}
                    <div class="market-dates-section" id="marketDates${index}">
                        <div class="market-dates-title arabic-text">🛒 أيام السوق القريبة:</div>
                        <div class="market-dates-list" id="marketDatesList${index}"></div>
                    </div>
                `;

                vaccinationScheduleDiv.appendChild(vaccinationItem);
            });

            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });

            // تحديث تواريخ السوق إذا كان يوم السوق محدد
            if (marketDay.value) {
                updateMarketDates();
            }
        }

        function updateMarketDates() {
            const selectedDay = parseInt(marketDay.value);

            if (isNaN(selectedDay) || currentVaccinationDates.length === 0) {
                // إخفاء جميع أقسام السوق
                currentVaccinationDates.forEach((_, index) => {
                    const marketSection = document.getElementById(`marketDates${index}`);
                    if (marketSection) {
                        marketSection.classList.remove('show');
                    }
                });
                return;
            }

            currentVaccinationDates.forEach((vaccination, index) => {
                const marketSection = document.getElementById(`marketDates${index}`);
                const marketList = document.getElementById(`marketDatesList${index}`);

                if (marketSection && marketList) {
                    const nearbyMarketDates = findNearbyMarketDates(vaccination.date, selectedDay);

                    if (nearbyMarketDates.length > 0) {
                        marketList.innerHTML = '';
                        nearbyMarketDates.forEach(marketDate => {
                            const dateItem = document.createElement('div');
                            dateItem.className = `market-date-item ${marketDate.isExact ? 'exact-match' : ''}`;
                            dateItem.textContent = marketDate.formattedDate;
                            if (marketDate.isExact) {
                                dateItem.textContent += ' 🎯';
                            }
                            marketList.appendChild(dateItem);
                        });
                        marketSection.classList.add('show');
                    } else {
                        marketSection.classList.remove('show');
                    }
                }
            });
        }

        function findNearbyMarketDates(vaccinationDate, marketDayOfWeek) {
            const nearbyDates = [];
            const vacDate = new Date(vaccinationDate);

            // البحث عن تواريخ السوق قبل وبعد موعد التلقيح (أسبوعين قبل وأسبوعين بعد)
            for (let weekOffset = -2; weekOffset <= 2; weekOffset++) {
                const searchDate = new Date(vacDate);
                searchDate.setDate(searchDate.getDate() + (weekOffset * 7));

                // العثور على يوم السوق في هذا الأسبوع
                const daysToMarket = (marketDayOfWeek - searchDate.getDay() + 7) % 7;
                searchDate.setDate(searchDate.getDate() + daysToMarket);

                const isExact = formatDate(searchDate) === formatDate(vacDate);
                const daysDiff = Math.abs((searchDate - vacDate) / (1000 * 60 * 60 * 24));

                // إضافة التاريخ إذا كان قريب (خلال 10 أيام) أو مطابق تماماً
                if (daysDiff <= 10 || isExact) {
                    nearbyDates.push({
                        date: searchDate,
                        formattedDate: formatDate(searchDate),
                        isExact: isExact,
                        daysDiff: daysDiff
                    });
                }
            }

            // ترتيب حسب القرب من موعد التلقيح
            return nearbyDates.sort((a, b) => a.daysDiff - b.daysDiff);
        }

        async function generatePDF() {
            try {
                downloadPdfBtn.disabled = true;
                downloadPdfBtn.innerHTML = '⏳ جاري إنشاء PDF...';

                const { jsPDF } = window.jspdf;

                // إخفاء العناصر التي لا نريدها في PDF
                pdfDownloadSection.style.visibility = 'hidden';
                const developerInfo = document.querySelector('.developer-info');
                const databaseSection = document.querySelector('.database-section');
                const originalDeveloperVisibility = developerInfo.style.visibility;
                const originalDatabaseVisibility = databaseSection.style.visibility;
                developerInfo.style.visibility = 'hidden';
                databaseSection.style.visibility = 'hidden';

                // انتظار قصير للتأكد من تطبيق التغييرات
                await new Promise(resolve => setTimeout(resolve, 200));

                // التقاط صورة للحاوية الرئيسية
                const container = document.querySelector('.container');

                const canvas = await html2canvas(container, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: true,
                    height: container.scrollHeight,
                    width: container.scrollWidth,
                    scrollX: 0,
                    scrollY: 0
                });

                console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);

                // التحقق من أن الصورة تم التقاطها بنجاح
                if (canvas.width === 0 || canvas.height === 0) {
                    throw new Error('فشل في التقاط الصورة - أبعاد غير صحيحة');
                }

                // إنشاء PDF
                const imgData = canvas.toDataURL('image/png');

                // التحقق من أن البيانات صحيحة
                if (!imgData || imgData === 'data:,') {
                    throw new Error('فشل في تحويل الصورة إلى بيانات');
                }

                const pdf = new jsPDF('p', 'mm', 'a4');

                // حساب أبعاد الصورة للصفحة
                const pdfWidth = pdf.internal.pageSize.getWidth(); // 210mm
                const pdfHeight = pdf.internal.pageSize.getHeight(); // 297mm

                console.log('PDF dimensions:', pdfWidth, 'x', pdfHeight);
                console.log('Canvas dimensions:', canvas.width, 'x', canvas.height);

                // حساب نسبة العرض إلى الطول للصورة الأصلية
                const imgAspectRatio = canvas.width / canvas.height;
                console.log('Image aspect ratio:', imgAspectRatio);

                // حساب الأبعاد للحفاظ على النسبة وضمان ظهور الصورة كاملة
                let finalWidth, finalHeight, x, y;

                // حساب الأبعاد بناءً على العرض الكامل
                const widthBasedHeight = pdfWidth / imgAspectRatio;

                // حساب الأبعاد بناءً على الطول الكامل
                const heightBasedWidth = pdfHeight * imgAspectRatio;

                if (widthBasedHeight <= pdfHeight) {
                    // الصورة تدخل في الصفحة عند استخدام العرض الكامل
                    finalWidth = pdfWidth;
                    finalHeight = widthBasedHeight;
                    x = 0;
                    y = (pdfHeight - finalHeight) / 2;
                    console.log('Using full width approach');
                } else {
                    // الصورة طويلة جداً، استخدم الطول الكامل
                    finalWidth = heightBasedWidth;
                    finalHeight = pdfHeight;
                    x = (pdfWidth - finalWidth) / 2;
                    y = 0;
                    console.log('Using full height approach');
                }

                console.log('Final dimensions:', finalWidth, 'x', finalHeight);
                console.log('Position:', x, ',', y);

                console.log('Position:', x, ',', y);

                // إضافة الصورة لتملأ الصفحة بالكامل
                pdf.addImage(imgData, 'PNG', x, y, finalWidth, finalHeight);

                // حفظ الملف مع اسم الطفل
                const childName = childNameInput.value.trim() || 'طفل';
                const birthDate = birthDateInput.value.replace(/\//g, '-');
                const fileName = `vaccination-schedule-${childName}-${birthDate}.pdf`;
                pdf.save(fileName);

                console.log('PDF تم إنشاؤه بنجاح');

            } catch (error) {
                console.error('خطأ في إنشاء PDF:', error);
                alert(`حدث خطأ أثناء إنشاء ملف PDF: ${error.message}`);
            } finally {
                // إعادة إظهار العناصر المخفية
                pdfDownloadSection.style.visibility = 'visible';
                const developerInfo = document.querySelector('.developer-info');
                const databaseSection = document.querySelector('.database-section');
                developerInfo.style.visibility = originalDeveloperVisibility || 'visible';
                databaseSection.style.visibility = originalDatabaseVisibility || 'visible';

                downloadPdfBtn.disabled = false;
                downloadPdfBtn.innerHTML = '📥 تحميل جدول التلقيح PDF';
            }
        }

        // وظائف إدارة المستخدمين
        function loadUsers() {
            const saved = localStorage.getItem('nursesUsersDB');
            if (saved) {
                usersDatabase = JSON.parse(saved);
            }
        }

        function saveUsers() {
            localStorage.setItem('nursesUsersDB', JSON.stringify(usersDatabase));
        }

        function checkCurrentUser() {
            const saved = localStorage.getItem('currentNurseUser');
            if (saved) {
                currentUser = JSON.parse(saved);
                showUserInfo();
                loadUserDatabase();
            }
        }

        function showUserInfo() {
            document.getElementById('currentUserName').textContent = currentUser.name;
            document.getElementById('currentCenter').textContent = currentUser.center;
            document.getElementById('currentRegion').textContent = currentUser.region;
            document.getElementById('userInfo').style.display = 'block';
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'none';
            document.getElementById('sidebarMenu').style.display = 'block';
            document.getElementById('miniSidebarPages').style.display = 'block';
        }

        function register() {
            const name = document.getElementById('registerName').value.trim();
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value.trim();
            const center = document.getElementById('registerCenter').value.trim();
            const region = document.getElementById('registerRegion').value.trim();

            if (!name || !username || !password || !center || !region) {
                alert('يرجى ملء جميع الحقول');
                return;
            }

            // التحقق من وجود اسم المستخدم
            if (usersDatabase.find(user => user.username === username)) {
                alert('اسم المستخدم موجود مسبقاً');
                return;
            }

            const newUser = {
                id: Date.now().toString(),
                name: name,
                username: username,
                password: password, // في التطبيق الحقيقي يجب تشفير كلمة المرور
                center: center,
                region: region,
                createdAt: new Date().toISOString()
            };

            usersDatabase.push(newUser);
            saveUsers();

            alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول');
            showLoginForm();

            // مسح الحقول
            document.getElementById('registerName').value = '';
            document.getElementById('registerUsername').value = '';
            document.getElementById('registerPassword').value = '';
            document.getElementById('registerCenter').value = '';
            document.getElementById('registerRegion').value = '';
        }

        function login() {
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value.trim();

            if (!username || !password) {
                alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            const user = usersDatabase.find(u => u.username === username && u.password === password);

            if (!user) {
                alert('اسم المستخدم أو كلمة المرور غير صحيحة');
                return;
            }

            currentUser = user;
            localStorage.setItem('currentNurseUser', JSON.stringify(currentUser));
            showUserInfo();
            loadUserDatabase();
            closeSidebar();

            alert(`مرحباً بك ${user.name}!`);

            // مسح الحقول
            document.getElementById('loginUsername').value = '';
            document.getElementById('loginPassword').value = '';
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentUser = null;
                localStorage.removeItem('currentNurseUser');

                // مسح قاعدة بيانات الأطفال الحالية
                childrenDatabase = [];
                displayChildrenList();

                // إخفاء النتائج
                resultsSection.style.display = 'none';
                childNameInput.value = '';
                birthDateInput.value = '';

                // إظهار نموذج تسجيل الدخول
                document.getElementById('userInfo').style.display = 'none';
                document.getElementById('loginForm').style.display = 'block';
                document.getElementById('registerForm').style.display = 'none';
                document.getElementById('sidebarMenu').style.display = 'none';
                document.getElementById('miniSidebarPages').style.display = 'none';

                alert('تم تسجيل الخروج بنجاح');
            }
        }

        function showLoginForm() {
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
        }

        function showRegisterForm() {
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';
        }

        function toggleSidebar() {
            const isOpen = sidebar.classList.contains('open');
            const container = document.querySelector('.container');
            const registryPage = document.getElementById('childrenRegistryPage');
            const dashboardPage = document.getElementById('dashboardPage');
            const managementPage = document.getElementById('vaccineManagementPage');

            if (isOpen) {
                // إغلاق الشريط الكامل وإظهار المصغر
                sidebar.classList.remove('open');
                overlay.classList.remove('active');
                container.classList.remove('sidebar-open');
                registryPage.classList.remove('sidebar-open');
                dashboardPage.classList.remove('sidebar-open');
                managementPage.classList.remove('sidebar-open');
                miniSidebar.classList.remove('hidden');
            } else {
                // فتح الشريط الكامل وإخفاء المصغر
                sidebar.classList.add('open');
                overlay.classList.add('active');
                container.classList.add('sidebar-open');
                registryPage.classList.add('sidebar-open');
                dashboardPage.classList.add('sidebar-open');
                managementPage.classList.add('sidebar-open');
                miniSidebar.classList.add('hidden');
            }
        }

        function closeSidebar() {
            const container = document.querySelector('.container');
            const registryPage = document.getElementById('childrenRegistryPage');
            const dashboardPage = document.getElementById('dashboardPage');
            const managementPage = document.getElementById('vaccineManagementPage');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            container.classList.remove('sidebar-open');
            registryPage.classList.remove('sidebar-open');
            dashboardPage.classList.remove('sidebar-open');
            managementPage.classList.remove('sidebar-open');
            miniSidebar.classList.remove('hidden');
        }

        // تهيئة الحالة الأولية
        function initializeSidebar() {
            miniSidebar.classList.remove('hidden');
        }

        // وظائف التنقل بين الصفحات
        function showMainPage() {
            document.getElementById('mainPage').style.display = 'block';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.sidebar-menu-item').classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelector('.mini-page-icon').classList.add('active');

            closeSidebar();
        }

        function showDashboard() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'block';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[1].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[1].classList.add('active');

            // تحديث لوحة القيادة
            updateDashboard();

            closeSidebar();
        }

        function showVaccineManagement() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'block';
            document.getElementById('childrenRegistryPage').style.display = 'none';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[2].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[2].classList.add('active');

            // تحديث صفحة تدبير اللقاحات
            initializeVaccineManagement();

            closeSidebar();
        }

        function showChildrenRegistry() {
            document.getElementById('mainPage').style.display = 'none';
            document.getElementById('dashboardPage').style.display = 'none';
            document.getElementById('vaccineManagementPage').style.display = 'none';
            document.getElementById('childrenRegistryPage').style.display = 'block';

            // تحديث قائمة الشريط الجانبي
            document.querySelectorAll('.sidebar-menu-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelectorAll('.sidebar-menu-item')[3].classList.add('active');

            // تحديث أيقونات الشريط المصغر
            document.querySelectorAll('.mini-page-icon').forEach(icon => {
                icon.classList.remove('active');
            });
            document.querySelectorAll('.mini-page-icon')[3].classList.add('active');

            // تحديث شبكة الأطفال
            updateChildrenGrid();

            closeSidebar();
        }

        // وظائف تدبير اللقاحات
        let vaccineStock = {};
        let vaccineUsageLog = [];

        function initializeVaccineManagement() {
            loadVaccineStock();
            displayStockGrid();
            displayNotifications();
            displayStockPrediction();
        }

        function loadVaccineStock() {
            if (!currentUser) return;

            const savedStock = localStorage.getItem(`vaccineStock_${currentUser.id}`);
            const savedLog = localStorage.getItem(`vaccineUsageLog_${currentUser.id}`);

            if (savedStock) {
                vaccineStock = JSON.parse(savedStock);
            } else {
                // تهيئة المخزون الافتراضي
                vaccineStock = {
                    'HB1': 50, 'BCG': 50, 'VPO3': 50, 'VPI3': 50,
                    'Rota2': 50, 'Rota3': 50, 'Penta1': 50, 'Penta3': 50,
                    'RR1': 50, 'Pneumo3': 50, 'RR2': 50, 'DTC1_18m': 50,
                    'VPO1_18m': 50, 'DTC2_5y': 50, 'VPO2_5y': 50, 'VAT': 50
                };
            }

            if (savedLog) {
                vaccineUsageLog = JSON.parse(savedLog);
            }
        }

        function saveVaccineStock() {
            if (!currentUser) return;
            localStorage.setItem(`vaccineStock_${currentUser.id}`, JSON.stringify(vaccineStock));
            localStorage.setItem(`vaccineUsageLog_${currentUser.id}`, JSON.stringify(vaccineUsageLog));
        }

        function displayStockGrid() {
            const stockGrid = document.getElementById('stockGrid');
            stockGrid.innerHTML = '';

            const vaccineDefinitions = {
                'HB1': { name: 'التهاب الكبد ب (عند الولادة)', frenchName: 'Hépatite B (à la naissance)' },
                'BCG': { name: 'السل', frenchName: 'BCG (Tuberculose)' },
                'VPO3': { name: 'شلل الأطفال الفموي الثالث', frenchName: 'VPO3 (Polio oral 3ème dose)' },
                'VPI3': { name: 'شلل الأطفال المحقون الثالث', frenchName: 'VPI3 (Polio injectable 3ème dose)' },
                'Rota2': { name: 'الروتا الثاني', frenchName: 'Rota2 (Rotavirus 2ème dose)' },
                'Rota3': { name: 'الروتا الثالث', frenchName: 'Rota3 (Rotavirus 3ème dose)' },
                'Penta1': { name: 'الخماسي الأول', frenchName: 'Penta1 (Pentavalent 1ère dose)' },
                'Penta3': { name: 'الخماسي الثالث', frenchName: 'Penta3 (Pentavalent 3ème dose)' },
                'RR1': { name: 'الحصبة والحصبة الألمانية الأول', frenchName: 'RR1 (Rougeole-Rubéole 1ère dose)' },
                'Pneumo3': { name: 'المكورات الرئوية الثالث', frenchName: 'Pneumo3 (Pneumocoque 3ème dose)' },
                'RR2': { name: 'الحصبة والحصبة الألمانية الثاني', frenchName: 'RR2 (Rougeole-Rubéole 2ème dose)' },
                'DTC1_18m': { name: 'الثلاثي الأول (18 شهر)', frenchName: 'DTC1 (Diphtérie-Tétanos-Coqueluche 18 mois)' },
                'VPO1_18m': { name: 'شلل الأطفال الأول (18 شهر)', frenchName: 'VPO1 (Polio oral 18 mois)' },
                'DTC2_5y': { name: 'الثلاثي الثاني (5 سنوات)', frenchName: 'DTC2 (Diphtérie-Tétanos-Coqueluche 5 ans)' },
                'VPO2_5y': { name: 'شلل الأطفال الثاني (5 سنوات)', frenchName: 'VPO2 (Polio oral 5 ans)' },
                'VAT': { name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
            };

            Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                const vaccine = vaccineDefinitions[vaccineKey];
                const currentStock = vaccineStock[vaccineKey] || 0;
                const status = getStockStatus(currentStock);

                const stockItem = document.createElement('div');
                stockItem.className = 'stock-item';
                stockItem.innerHTML = `
                    <h4>${vaccine.name}</h4>
                    <div style="font-size: 0.85em; color: #6c757d; font-style: italic; margin-bottom: 10px;">
                        ${vaccine.frenchName}
                    </div>
                    <div class="stock-display">المخزون: ${currentStock}</div>
                    <span class="stock-status ${status.class}">${status.text}</span>
                    <div class="stock-controls">
                        <input type="number" class="stock-input" id="stock_${vaccineKey}"
                               value="${currentStock}" min="0" max="1000">
                        <button onclick="updateStock('${vaccineKey}')" class="btn btn-sm btn-primary">تحديث</button>
                    </div>
                `;
                stockGrid.appendChild(stockItem);
            });
        }

        function getStockStatus(stock) {
            if (stock >= 30) {
                return { class: 'good', text: 'متوفر' };
            } else if (stock >= 10) {
                return { class: 'warning', text: 'منخفض' };
            } else {
                return { class: 'critical', text: 'نفاد' };
            }
        }

        function updateStock(vaccineKey) {
            const input = document.getElementById(`stock_${vaccineKey}`);
            const newStock = parseInt(input.value) || 0;

            vaccineStock[vaccineKey] = newStock;
            saveVaccineStock();
            displayStockGrid();
            displayStockPrediction();

            // إضافة إشعار التحديث
            addNotification(`تم تحديث مخزون ${getVaccineName(vaccineKey)} إلى ${newStock}`, 'success');
        }

        function updateAllStock() {
            Object.keys(vaccineStock).forEach(vaccineKey => {
                const input = document.getElementById(`stock_${vaccineKey}`);
                if (input) {
                    vaccineStock[vaccineKey] = parseInt(input.value) || 0;
                }
            });

            saveVaccineStock();
            displayStockGrid();
            displayStockPrediction();
            addNotification('تم تحديث جميع المخزونات بنجاح', 'success');
        }

        function useVaccine(vaccineKey, childName) {
            if (vaccineStock[vaccineKey] > 0) {
                vaccineStock[vaccineKey]--;

                // تسجيل الاستخدام
                vaccineUsageLog.push({
                    vaccine: vaccineKey,
                    childName: childName,
                    date: new Date().toISOString(),
                    displayDate: new Date().toLocaleDateString('ar-MA')
                });

                saveVaccineStock();

                // إضافة إشعار الاستخدام
                const vaccineName = getVaccineName(vaccineKey);
                addNotification(`تم استخدام لقاح ${vaccineName} للطفل ${childName}`, 'info');

                // تحديث العرض إذا كانت الصفحة مفتوحة
                if (document.getElementById('vaccineManagementPage').style.display === 'block') {
                    displayStockGrid();
                    displayNotifications();
                    displayStockPrediction();
                }
            }
        }

        function getVaccineName(vaccineKey) {
            const names = {
                'HB1': 'التهاب الكبد ب', 'BCG': 'السل', 'VPO3': 'شلل الأطفال الفموي الثالث',
                'VPI3': 'شلل الأطفال المحقون الثالث', 'Rota2': 'الروتا الثاني', 'Rota3': 'الروتا الثالث',
                'Penta1': 'الخماسي الأول', 'Penta3': 'الخماسي الثالث', 'RR1': 'الحصبة والحصبة الألمانية الأول',
                'Pneumo3': 'المكورات الرئوية الثالث', 'RR2': 'الحصبة والحصبة الألمانية الثاني',
                'DTC1_18m': 'الثلاثي الأول', 'VPO1_18m': 'شلل الأطفال الأول', 'DTC2_5y': 'الثلاثي الثاني',
                'VPO2_5y': 'شلل الأطفال الثاني', 'VAT': 'الكزاز للحوامل'
            };
            return names[vaccineKey] || vaccineKey;
        }

        function addNotification(message, type = 'info') {
            const notification = {
                id: Date.now(),
                message: message,
                type: type,
                timestamp: new Date().toLocaleString('ar-MA')
            };

            // إضافة للقائمة (الاحتفاظ بآخر 10 إشعارات)
            if (!window.notifications) window.notifications = [];
            window.notifications.unshift(notification);
            if (window.notifications.length > 10) {
                window.notifications = window.notifications.slice(0, 10);
            }

            displayNotifications();
        }

        function displayNotifications() {
            const notificationsList = document.getElementById('notificationsList');
            if (!window.notifications || window.notifications.length === 0) {
                notificationsList.innerHTML = '<p style="color: #6c757d; text-align: center;">لا توجد إشعارات حالياً</p>';
                return;
            }

            notificationsList.innerHTML = '';
            window.notifications.forEach(notification => {
                const notificationItem = document.createElement('div');
                notificationItem.className = `notification-item ${notification.type}`;
                notificationItem.innerHTML = `
                    <div style="font-weight: bold;">${notification.message}</div>
                    <div style="font-size: 0.85em; color: #6c757d; margin-top: 5px;">
                        ${notification.timestamp}
                    </div>
                `;
                notificationsList.appendChild(notificationItem);
            });
        }

        function displayStockPrediction() {
            const tbody = document.getElementById('predictionTableBody');
            tbody.innerHTML = '';

            // حساب معدل الاستهلاك الشهري لكل لقاح
            const monthlyUsage = calculateMonthlyUsage();

            Object.keys(vaccineStock).forEach(vaccineKey => {
                const currentStock = vaccineStock[vaccineKey];
                const monthlyUse = monthlyUsage[vaccineKey] || 0;

                const month1 = Math.max(0, currentStock - monthlyUse);
                const month2 = Math.max(0, month1 - monthlyUse);
                const month3 = Math.max(0, month2 - monthlyUse);

                const status = month3 <= 5 ? 'نفاد متوقع' : month3 <= 15 ? 'خصاص متوقع' : 'متوفر';
                const statusClass = month3 <= 5 ? 'shortage-highlight' : '';

                const row = document.createElement('tr');
                row.className = statusClass;
                row.innerHTML = `
                    <td>${getVaccineName(vaccineKey)}</td>
                    <td>${currentStock}</td>
                    <td>${month1}</td>
                    <td>${month2}</td>
                    <td>${month3}</td>
                    <td>${status}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function calculateMonthlyUsage() {
            const usage = {};
            const now = new Date();
            const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());

            // تهيئة العدادات
            Object.keys(vaccineStock).forEach(key => {
                usage[key] = 0;
            });

            // حساب الاستخدام في آخر 3 أشهر
            vaccineUsageLog.forEach(log => {
                const logDate = new Date(log.date);
                if (logDate >= threeMonthsAgo) {
                    usage[log.vaccine] = (usage[log.vaccine] || 0) + 1;
                }
            });

            // حساب المتوسط الشهري
            Object.keys(usage).forEach(key => {
                usage[key] = Math.ceil(usage[key] / 3);
            });

            return usage;
        }

        function downloadStockReport() {
            // إنشاء محتوى التقرير
            const reportContent = generateStockReportHTML();

            // إنشاء نافذة جديدة للطباعة
            const printWindow = window.open('', '_blank');
            printWindow.document.write(reportContent);
            printWindow.document.close();

            // طباعة التقرير كـ PDF
            setTimeout(() => {
                printWindow.print();
            }, 500);
        }

        function generateStockReportHTML() {
            const now = new Date();
            const reportDate = now.toLocaleDateString('ar-MA');
            const centerName = currentUser ? currentUser.center : 'المركز الصحي';
            const nurseName = currentUser ? currentUser.name : 'الممرض';

            let stockTableRows = '';
            Object.keys(vaccineStock).forEach(vaccineKey => {
                const stock = vaccineStock[vaccineKey];
                const status = getStockStatus(stock);
                stockTableRows += `
                    <tr>
                        <td>${getVaccineName(vaccineKey)}</td>
                        <td>${stock}</td>
                        <td>${status.text}</td>
                    </tr>
                `;
            });

            const monthlyUsage = calculateMonthlyUsage();
            let predictionTableRows = '';
            Object.keys(vaccineStock).forEach(vaccineKey => {
                const currentStock = vaccineStock[vaccineKey];
                const monthlyUse = monthlyUsage[vaccineKey] || 0;
                const month1 = Math.max(0, currentStock - monthlyUse);
                const month2 = Math.max(0, month1 - monthlyUse);
                const month3 = Math.max(0, month2 - monthlyUse);
                const status = month3 <= 5 ? 'نفاد متوقع' : month3 <= 15 ? 'خصاص متوقع' : 'متوفر';

                predictionTableRows += `
                    <tr ${month3 <= 5 ? 'style="background: #ffebee; color: #c62828;"' : ''}>
                        <td>${getVaccineName(vaccineKey)}</td>
                        <td>${currentStock}</td>
                        <td>${month1}</td>
                        <td>${month2}</td>
                        <td>${month3}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });

            return `
                <!DOCTYPE html>
                <html dir="rtl" lang="ar">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير مخزون اللقاحات</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .info { margin-bottom: 20px; }
                        table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        th { background: #f5f5f5; font-weight: bold; }
                        .section-title { color: #28a745; font-size: 1.2em; margin: 20px 0 10px 0; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير مخزون اللقاحات</h1>
                        <h2>${centerName}</h2>
                    </div>

                    <div class="info">
                        <p><strong>الممرض:</strong> ${nurseName}</p>
                        <p><strong>تاريخ التقرير:</strong> ${reportDate}</p>
                    </div>

                    <div class="section-title">المخزون الحالي</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اللقاح</th>
                                <th>الكمية</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${stockTableRows}
                        </tbody>
                    </table>

                    <div class="section-title">توقعات المخزون (3 أشهر قادمة)</div>
                    <table>
                        <thead>
                            <tr>
                                <th>اللقاح</th>
                                <th>المخزون الحالي</th>
                                <th>الشهر القادم</th>
                                <th>الشهر الثاني</th>
                                <th>الشهر الثالث</th>
                                <th>حالة التوفر</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${predictionTableRows}
                        </tbody>
                    </table>
                </body>
                </html>
            `;
        }

        // وظائف لوحة القيادة
        function updateDashboard() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            const period = document.getElementById('periodFilter').value;

            // تطبيق فلاتر التاريخ
            let filteredChildren = getFilteredChildren(startDate, endDate, period);

            // حساب الإحصائيات
            const stats = calculateVaccinationStats(filteredChildren);

            // عرض الإحصائيات
            displayStats(stats);
            displayDetailedStats(stats);
        }

        function getFilteredChildren(startDate, endDate, period) {
            const allChildren = getAllChildren();
            let filtered = allChildren;

            // تطبيق فلتر الفترة المحددة مسبقاً
            if (period !== 'all') {
                const now = new Date();
                let filterDate = new Date();

                switch(period) {
                    case 'month':
                        filterDate.setMonth(now.getMonth() - 1);
                        break;
                    case 'quarter':
                        filterDate.setMonth(now.getMonth() - 3);
                        break;
                    case 'year':
                        filterDate.setFullYear(now.getFullYear() - 1);
                        break;
                }

                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate >= filterDate;
                });
            }

            // تطبيق فلاتر التاريخ المخصصة
            if (startDate) {
                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate >= new Date(startDate);
                });
            }

            if (endDate) {
                filtered = filtered.filter(child => {
                    const childDate = new Date(child.birthDate);
                    return childDate <= new Date(endDate);
                });
            }

            return filtered;
        }

        function getAllChildren() {
            const children = [];
            const users = JSON.parse(localStorage.getItem('users') || '[]');

            users.forEach(user => {
                if (user.children) {
                    user.children.forEach(child => {
                        children.push({
                            ...child,
                            userId: user.username
                        });
                    });
                }
            });

            return children;
        }

        function calculateVaccinationStats(children) {
            const stats = {
                totalChildren: children.length,
                expectedBirths: children.length, // يمكن تعديلها حسب البيانات المتوفرة
                vaccines: {}
            };

            // تعريف اللقاحات وأعمارها المستهدفة
            const vaccineDefinitions = {
                'HB1': { targetAge: 0, name: 'التهاب الكبد ب (عند الولادة)', frenchName: 'Hépatite B (à la naissance)' },
                'BCG': { targetAge: 0, name: 'السل', frenchName: 'BCG (Tuberculose)' },
                'VPO3': { targetAge: 365, name: 'شلل الأطفال الفموي الثالث', frenchName: 'VPO3 (Polio oral 3ème dose)' },
                'VPI3': { targetAge: 365, name: 'شلل الأطفال المحقون الثالث', frenchName: 'VPI3 (Polio injectable 3ème dose)' },
                'Rota2': { targetAge: 365, name: 'الروتا الثاني', frenchName: 'Rota2 (Rotavirus 2ème dose)' },
                'Rota3': { targetAge: 365, name: 'الروتا الثالث', frenchName: 'Rota3 (Rotavirus 3ème dose)' },
                'Penta1': { targetAge: 365, name: 'الخماسي الأول', frenchName: 'Penta1 (Pentavalent 1ère dose)' },
                'Penta3': { targetAge: 365, name: 'الخماسي الثالث', frenchName: 'Penta3 (Pentavalent 3ème dose)' },
                'RR1': { targetAge: 270, name: 'الحصبة والحصبة الألمانية الأول', frenchName: 'RR1 (Rougeole-Rubéole 1ère dose)' },
                'Pneumo3': { targetAge: 365, name: 'المكورات الرئوية الثالث', frenchName: 'Pneumo3 (Pneumocoque 3ème dose)' },
                'RR2': { targetAge: 548, name: 'الحصبة والحصبة الألمانية الثاني', frenchName: 'RR2 (Rougeole-Rubéole 2ème dose)' },
                'DTC1_18m': { targetAge: 548, name: 'الثلاثي الأول (18 شهر)', frenchName: 'DTC1 (Diphtérie-Tétanos-Coqueluche 18 mois)' },
                'VPO1_18m': { targetAge: 548, name: 'شلل الأطفال الأول (18 شهر)', frenchName: 'VPO1 (Polio oral 18 mois)' },
                'DTC2_5y': { targetAge: 1825, name: 'الثلاثي الثاني (5 سنوات)', frenchName: 'DTC2 (Diphtérie-Tétanos-Coqueluche 5 ans)' },
                'VPO2_5y': { targetAge: 1825, name: 'شلل الأطفال الثاني (5 سنوات)', frenchName: 'VPO2 (Polio oral 5 ans)' },
                'VAT': { targetAge: 0, name: 'الكزاز للحوامل', frenchName: 'VAT (Tétanos femmes enceintes)' }
            };

            // حساب إحصائيات كل لقاح
            Object.keys(vaccineDefinitions).forEach(vaccineKey => {
                const vaccine = vaccineDefinitions[vaccineKey];
                const eligibleChildren = children.filter(child => {
                    const ageInDays = (new Date() - new Date(child.birthDate)) / (1000 * 60 * 60 * 24);
                    return ageInDays >= vaccine.targetAge;
                });

                const vaccinatedChildren = eligibleChildren.filter(child => {
                    return child.vaccinations && child.vaccinations[vaccineKey] === true;
                });

                const coverage = eligibleChildren.length > 0 ?
                    (vaccinatedChildren.length / eligibleChildren.length) * 100 : 0;

                stats.vaccines[vaccineKey] = {
                    name: vaccine.name,
                    frenchName: vaccine.frenchName,
                    eligible: eligibleChildren.length,
                    vaccinated: vaccinatedChildren.length,
                    coverage: coverage.toFixed(1),
                    status: coverage >= 95 ? 'ممتاز' : coverage >= 80 ? 'جيد' : coverage >= 60 ? 'متوسط' : 'ضعيف'
                };
            });

            return stats;
        }

        function displayStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = '';

            // إحصائيات عامة
            const generalStats = [
                {
                    title: 'إجمالي الأطفال المسجلين',
                    value: stats.totalChildren,
                    description: 'العدد الكلي للأطفال في قاعدة البيانات',
                    color: '#6f42c1'
                },
                {
                    title: 'متوسط التغطية التلقيحية',
                    value: calculateAverageCoverage(stats) + '%',
                    description: 'المتوسط العام لجميع اللقاحات',
                    color: '#28a745'
                },
                {
                    title: 'اللقاحات المكتملة',
                    value: countCompletedVaccines(stats),
                    description: 'عدد اللقاحات التي حققت تغطية +95%',
                    color: '#17a2b8'
                },
                {
                    title: 'اللقاحات المتأخرة',
                    value: countDelayedVaccines(stats),
                    description: 'عدد اللقاحات التي تحتاج متابعة',
                    color: '#dc3545'
                }
            ];

            generalStats.forEach(stat => {
                const card = createStatCard(stat);
                statsGrid.appendChild(card);
            });
        }

        function calculateAverageCoverage(stats) {
            const coverages = Object.values(stats.vaccines).map(v => parseFloat(v.coverage));
            const average = coverages.reduce((sum, coverage) => sum + coverage, 0) / coverages.length;
            return average.toFixed(1);
        }

        function countCompletedVaccines(stats) {
            return Object.values(stats.vaccines).filter(v => parseFloat(v.coverage) >= 95).length;
        }

        function countDelayedVaccines(stats) {
            return Object.values(stats.vaccines).filter(v => parseFloat(v.coverage) < 80).length;
        }

        function createStatCard(stat) {
            const card = document.createElement('div');
            card.className = 'stat-card';
            card.innerHTML = `
                <h3 style="color: ${stat.color}">${stat.title}</h3>
                <div class="stat-value" style="color: ${stat.color}">${stat.value}</div>
                <div class="stat-description">${stat.description}</div>
            `;
            return card;
        }

        function displayDetailedStats(stats) {
            const tbody = document.getElementById('detailedStatsBody');
            tbody.innerHTML = '';

            Object.entries(stats.vaccines).forEach(([key, vaccine]) => {
                const row = document.createElement('tr');
                const statusColor = getStatusColor(vaccine.status);

                row.innerHTML = `
                    <td>
                        <div class="vaccine-name-container">
                            <div class="arabic-text" style="font-weight: bold; margin-bottom: 3px;">${vaccine.name}</div>
                            <div class="french-text" style="font-size: 0.85em; color: #6c757d; font-style: italic;">${vaccine.frenchName}</div>
                        </div>
                    </td>
                    <td>${vaccine.eligible}</td>
                    <td>${vaccine.vaccinated}</td>
                    <td>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${vaccine.coverage}%; background: ${statusColor}">
                                ${vaccine.coverage}%
                            </div>
                        </div>
                    </td>
                    <td><span style="color: ${statusColor}; font-weight: bold">${vaccine.status}</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        function getStatusColor(status) {
            switch(status) {
                case 'ممتاز': return '#28a745';
                case 'جيد': return '#17a2b8';
                case 'متوسط': return '#ffc107';
                case 'ضعيف': return '#dc3545';
                default: return '#6c757d';
            }
        }

        // تهيئة التواريخ الافتراضية
        function initializeDashboard() {
            const now = new Date();
            const startOfYear = new Date(now.getFullYear(), 0, 1);

            document.getElementById('startDate').value = startOfYear.toISOString().split('T')[0];
            document.getElementById('endDate').value = now.toISOString().split('T')[0];

            updateDashboard();
        }

        // تحديث شبكة الأطفال في صفحة السجل
        function updateChildrenGrid() {
            const grid = document.getElementById('childrenGrid');

            if (childrenDatabase.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">لا توجد أطفال مسجلين بعد</div>';
                return;
            }

            grid.innerHTML = '';
            childrenDatabase.forEach(child => {
                const childCard = createChildCard(child);
                grid.appendChild(childCard);
            });
        }

        // إنشاء بطاقة طفل للشبكة
        function createChildCard(child) {
            const card = document.createElement('div');
            card.className = 'child-card';
            card.style.cssText = `
                background: white;
                border: 1px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            `;

            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px)';
                card.style.boxShadow = '0 5px 20px rgba(0,0,0,0.15)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
                card.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
            });

            const completedCount = child.vaccinations ? child.vaccinations.filter(v => v.completed).length : 0;
            const totalCount = child.vaccinations ? child.vaccinations.length : 0;
            const completionPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;

            card.innerHTML = `
                <h3 style="color: #17a2b8; margin-bottom: 10px;">${child.name}</h3>
                <p style="color: #666; margin-bottom: 15px;">📅 ${child.birthDate}</p>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>التلقيحات المكتملة:</span>
                        <span style="font-weight: bold;">${completedCount}/${totalCount}</span>
                    </div>
                    <div style="background: #e9ecef; height: 8px; border-radius: 4px; overflow: hidden;">
                        <div style="background: ${completionPercentage === 100 ? '#28a745' : '#17a2b8'}; height: 100%; width: ${completionPercentage}%; transition: width 0.3s ease;"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px; font-size: 0.9rem; color: #666;">${completionPercentage}%</div>
                </div>
                <div style="display: flex; gap: 10px;">
                    <button onclick="loadChildData('${child.id}')" style="flex: 1; background: #17a2b8; color: white; border: none; padding: 8px; border-radius: 5px; cursor: pointer;">📋 عرض</button>
                    <button onclick="deleteChild('${child.id}')" style="background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">🗑️</button>
                </div>
            `;

            return card;
        }

        // البحث في سجل الأطفال
        function setupSearch() {
            const searchBox = document.getElementById('searchBox');
            if (searchBox) {
                searchBox.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    const grid = document.getElementById('childrenGrid');
                    const cards = grid.querySelectorAll('.child-card');

                    cards.forEach(card => {
                        const name = card.querySelector('h3').textContent.toLowerCase();
                        const birthDate = card.querySelector('p').textContent.toLowerCase();

                        if (name.includes(searchTerm) || birthDate.includes(searchTerm)) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            }
        }

        // وظائف قاعدة البيانات
        function loadDatabase() {
            if (!currentUser) return;

            const saved = localStorage.getItem(`childrenVaccinationDB_${currentUser.id}`);
            if (saved) {
                childrenDatabase = JSON.parse(saved);
                displayChildrenList();
            }
        }

        function loadUserDatabase() {
            const saved = localStorage.getItem(`childrenVaccinationDB_${currentUser.id}`);
            if (saved) {
                childrenDatabase = JSON.parse(saved);
                displayChildrenList();
            } else {
                childrenDatabase = [];
                displayChildrenList();
            }
        }

        function saveDatabase() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً');
                return;
            }
            localStorage.setItem(`childrenVaccinationDB_${currentUser.id}`, JSON.stringify(childrenDatabase));
        }

        function saveChildToDatabase(name, birthDate, vaccinationDates) {
            const childId = Date.now().toString();
            const existingChildIndex = childrenDatabase.findIndex(child =>
                child.name === name && child.birthDate === birthDate
            );

            const childData = {
                id: childId,
                name: name,
                birthDate: birthDate,
                vaccinationDates: vaccinationDates,
                completedVaccinations: {},
                createdAt: new Date().toISOString()
            };

            if (existingChildIndex !== -1) {
                // تحديث الطفل الموجود
                childData.id = childrenDatabase[existingChildIndex].id;
                childData.completedVaccinations = childrenDatabase[existingChildIndex].completedVaccinations || {};
                childrenDatabase[existingChildIndex] = childData;
                saveDatabase();
                return childData.id;
            } else {
                // إضافة طفل جديد
                childrenDatabase.push(childData);
                saveDatabase();
                return childId;
            }
        }

        function displayChildrenList() {
            if (childrenDatabase.length === 0) {
                childrenList.innerHTML = `
                    <div style="text-align: center; color: #999; grid-column: 1/-1; padding: 40px;">
                        لا توجد سجلات بعد. ابدأ بحساب مواعيد التلقيح لطفل جديد.
                    </div>
                `;
                clearDatabaseBtn.style.display = 'none';
                return;
            }

            clearDatabaseBtn.style.display = 'block';
            childrenList.innerHTML = '';

            // عرض آخر 4 أطفال فقط
            const recentChildren = childrenDatabase.slice(-4).reverse();

            recentChildren.forEach(child => {
                const completedCount = Object.keys(child.completedVaccinations || {}).length;
                const totalCount = child.vaccinationDates.length;
                const progressPercent = Math.round((completedCount / totalCount) * 100);

                const childCard = document.createElement('div');
                childCard.className = 'child-card';
                childCard.innerHTML = `
                    <div class="child-name">${child.name}</div>
                    <div class="child-birth-date">📅 ${child.birthDate}</div>
                    <div class="child-progress">
                        📊 التقدم: ${completedCount}/${totalCount} (${progressPercent}%)
                    </div>
                    <button class="delete-child-btn" onclick="deleteChild('${child.id}')">حذف</button>
                `;

                childCard.addEventListener('click', (e) => {
                    if (!e.target.classList.contains('delete-child-btn')) {
                        loadChildData(child.id);
                    }
                });

                childrenList.appendChild(childCard);
            });

            // إضافة رسالة إذا كان هناك أطفال أكثر من 4
            if (childrenDatabase.length > 4) {
                const moreInfo = document.createElement('div');
                moreInfo.style.cssText = 'text-align: center; color: #666; margin-top: 15px; font-style: italic;';
                moreInfo.innerHTML = `وهناك ${childrenDatabase.length - 4} طفل/أطفال أخرى في السجل الكامل`;
                childrenList.appendChild(moreInfo);
            }
        }

        function loadChildData(childId) {
            const child = childrenDatabase.find(c => c.id === childId);
            if (!child) return;

            // تحديث الحقول
            childNameInput.value = child.name;
            birthDateInput.value = child.birthDate;

            // تحديث المتغيرات الحالية
            currentChildId = childId;
            currentVaccinationDates = child.vaccinationDates;
            currentBirthDate = parseDate(child.birthDate);

            // عرض النتائج مع حالة التلقيحات
            displayResults(child.vaccinationDates, child.name, child.birthDate, child.completedVaccinations);
            marketInput.style.display = 'flex';
            pdfDownloadSection.style.display = 'block';

            // تمييز الطفل المحدد
            document.querySelectorAll('.child-card').forEach(card => card.classList.remove('selected'));
            event.target.closest('.child-card').classList.add('selected');

            // التمرير إلى النتائج
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        function deleteChild(childId) {
            if (confirm('هل أنت متأكد من حذف سجل هذا الطفل؟')) {
                childrenDatabase = childrenDatabase.filter(child => child.id !== childId);
                saveDatabase();
                displayChildrenList();

                // إذا كان الطفل المحذوف هو المعروض حالياً، امسح النتائج
                if (currentChildId === childId) {
                    childNameInput.value = '';
                    birthDateInput.value = '';
                    resultsSection.style.display = 'none';
                    currentChildId = null;
                }
            }
        }

        function clearDatabase() {
            if (!currentUser) {
                alert('يجب تسجيل الدخول أولاً');
                return;
            }

            if (confirm('هل أنت متأكد من حذف جميع السجلات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                childrenDatabase = [];
                localStorage.removeItem(`childrenVaccinationDB_${currentUser.id}`);
                displayChildrenList();

                // مسح النتائج الحالية
                childNameInput.value = '';
                birthDateInput.value = '';
                resultsSection.style.display = 'none';
                currentChildId = null;
            }
        }

        function toggleVaccination(vaccinationIndex) {
            if (!currentChildId) return;

            const child = childrenDatabase.find(c => c.id === currentChildId);
            if (!child) return;

            if (!child.completedVaccinations) {
                child.completedVaccinations = {};
            }

            // تبديل حالة التلقيح
            if (child.completedVaccinations[vaccinationIndex]) {
                delete child.completedVaccinations[vaccinationIndex];
            } else {
                child.completedVaccinations[vaccinationIndex] = {
                    completedAt: new Date().toISOString(),
                    completedDate: new Date().toLocaleDateString('ar-MA')
                };

                // استخدام اللقاح من المخزون
                const vaccinationData = child.vaccinationDates[vaccinationIndex];
                if (vaccinationData && vaccinationData.vaccine) {
                    useVaccine(vaccinationData.vaccine, child.name);
                }
            }

            saveDatabase();
            displayChildrenList();

            // تحديث عرض النتائج
            displayResults(child.vaccinationDates, child.name, child.birthDate, child.completedVaccinations);
        }

        document.addEventListener('DOMContentLoaded', function() {
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
                initializeDashboard();
                initializeVaccineManagement();
            }, 100);
        });
    </script>
</body>
</html>
